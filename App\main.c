#include "./PORT/port.h"

#include "../System/config.h"

#include "./BSP/LED/led.h"
#include "./BSP/DEBUG/debug.h"
#include "./BSP/OLED/OLED.h"
#include "./BSP/KEY/key.h"
#include "./BSP/ENCODER/encoder.h"
#include "./BSP/TRACK/track.h"
#include "./BSP/JYxx/jyxx.h"
#include "./BSP/MOTOR/motor.h"

#include "./MALLOC/malloc.h"
#include "./USMART/usmart.h"

#include "FreeRTOS.h"
#include "task.h"

#include "app_state.h"
#include "app_key.h"
#include "app_ui.h"
#include "app_motor.h"

/**
 * @brief       系统初始化函数
 * @note        该函数会初始化系统时钟、延时、调试串口、USMART、内存池等
 * @retval      无
 */
void sys_init(void)
{
	sys_stm32_clock_init(336, 8, 2, 7); /* 设置时钟,168Mhz */
	debug_init();						/* 初始化调试 */
	delay_init(168);					/* 延时初始化 */
	SysTick_Config(168000);				/* 系统时钟抵滴答初始化 */
#if CFG_DEBUG_USMART_EN == CFG_ENABLE
	usmart_dev.init(84); /* 初始化USMART */
#endif
	sys_check_rst();	 /* 检查复位原因 */
	my_mem_init(SRAMIN); /* 初始化内部SRAM内存池 */
#if CFG_IWDG_EN == CFG_ENABLE
	iwdg_init(CFG_IWDG_PRESCALER, CFG_IWDG_RELOAD); // 1000ms看门狗
#endif
}

/**
 * @brief       硬件初始化函数
 * @note        该函数会初始化LED、OLED、按键等硬件
 * @retval      无
 */
void board_init(void)
{
	led_init();		/* 初始化LED */
	OLED_Init();	/* 初始化OLED */
	key_init();		/* 初始化按键 */
	encoder_init(); // 初始化编码器
	track_init();	// 初始化跟踪传感器
	jyxx_init();	// 初始化JY60
	motor_init();	// 初始化电机
}

/**
 * @brief       应用程序初始化函数
 * @note        该函数会创建系统状态任务、按键状态任务和OLED显示任务
 * @retval      无
 */
void app_init(void)
{
	if (app_state_task_start() == 0)
		debug_printf("app_state_task_start 任务创建成功\r\n");
	else
		debug_printf("app_state_task_start 任务创建失败\r\n");

	if (app_key_task_start() == 0)
		debug_printf("app_key_task_start 任务创建成功\r\n");
	else
		debug_printf("app_key_task_start 任务创建失败\r\n");

	if (app_ui_task_start() == 0)
		debug_printf("app_ui_task_start 任务创建成功\r\n");
	else
		debug_printf("app_ui_task_start 任务创建失败\r\n");

	if (app_motor_task_start() == 0)
		debug_printf("app_motor_task_start 任务创建成功\r\n");
	else
		debug_printf("app_motor_task_start 任务创建失败\r\n");
}

int main()
{
	sys_init();
	board_init();
	app_init();
	vTaskStartScheduler();

	while (1)
	{
	}
}
