#ifndef __ULTRASOUND_H_
#define __ULTRASOUND_H_

#include "./PORT/port.h"
typedef struct
{
    uint8_t edge_state;
    uint16_t tim_overflow_counter;
    uint32_t t1;            //	上升沿时间
    uint32_t t2;            //	下降沿时间
    uint32_t high_level_us; //	高电平持续时间
    float distance;
} Hcsr04InfoTypeDef;
/**
 * @brief       初始化超声波模块
 * @param       无
 * @retval      无
 */
void ultrasound_init(void);

/**
 * @brief       获取超声波距离
 * @param       sensor_id: 传感器编号
 * @retval      距离值(单位:cm)，返回0表示测量失败
 */
uint16_t ultrasound_get_distance(uint8_t sensor_id);

/**
 * @brief       触发超声波测距
 * @param       sensor_id: 传感器编号
 * @retval      无
 */
void ultrasound_trigger(uint8_t sensor_id);

/**
 * @brief       超声波回声中断处理函数
 * @param       五
 * @retval      无
 */
void ultrasound_echo_handler(void);

/**
 * @brief       获取超声波模块状态
 * @param       sensor_id: 传感器编号
 * @retval      0:空闲 1:测量中
 */
uint8_t ultrasound_get_status(uint8_t sensor_id);

#endif /* __ULTRASOUND_H_ */
