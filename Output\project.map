Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to sys.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to sys.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to sys.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to sys.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to gpio.o(i.EXTI0_IRQHandler) for EXTI0_IRQHandler
    startup_stm32f407xx.o(RESET) refers to gpio.o(i.EXTI1_IRQHandler) for EXTI1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to gpio.o(i.EXTI2_IRQHandler) for EXTI2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to gpio.o(i.EXTI3_IRQHandler) for EXTI3_IRQHandler
    startup_stm32f407xx.o(RESET) refers to gpio.o(i.EXTI4_IRQHandler) for EXTI4_IRQHandler
    startup_stm32f407xx.o(RESET) refers to gpio.o(i.EXTI9_5_IRQHandler) for EXTI9_5_IRQHandler
    startup_stm32f407xx.o(RESET) refers to tim.o(i.TIM1_BRK_TIM9_IRQHandler) for TIM1_BRK_TIM9_IRQHandler
    startup_stm32f407xx.o(RESET) refers to tim.o(i.TIM1_UP_TIM10_IRQHandler) for TIM1_UP_TIM10_IRQHandler
    startup_stm32f407xx.o(RESET) refers to tim.o(i.TIM1_TRG_COM_TIM11_IRQHandler) for TIM1_TRG_COM_TIM11_IRQHandler
    startup_stm32f407xx.o(RESET) refers to tim.o(i.TIM1_CC_IRQHandler) for TIM1_CC_IRQHandler
    startup_stm32f407xx.o(RESET) refers to tim.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to tim.o(i.TIM3_IRQHandler) for TIM3_IRQHandler
    startup_stm32f407xx.o(RESET) refers to usmart_port.o(i.TIM4_IRQHandler) for TIM4_IRQHandler
    startup_stm32f407xx.o(RESET) refers to usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to usart.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to usart.o(i.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f407xx.o(RESET) refers to gpio.o(i.EXTI15_10_IRQHandler) for EXTI15_10_IRQHandler
    startup_stm32f407xx.o(RESET) refers to tim.o(i.TIM8_BRK_TIM12_IRQHandler) for TIM8_BRK_TIM12_IRQHandler
    startup_stm32f407xx.o(RESET) refers to tim.o(i.TIM8_UP_TIM13_IRQHandler) for TIM8_UP_TIM13_IRQHandler
    startup_stm32f407xx.o(RESET) refers to tim.o(i.TIM8_TRG_COM_TIM14_IRQHandler) for TIM8_TRG_COM_TIM14_IRQHandler
    startup_stm32f407xx.o(RESET) refers to tim.o(i.TIM8_CC_IRQHandler) for TIM8_CC_IRQHandler
    startup_stm32f407xx.o(RESET) refers to tim.o(i.TIM5_IRQHandler) for TIM5_IRQHandler
    startup_stm32f407xx.o(RESET) refers to usart.o(i.UART4_IRQHandler) for UART4_IRQHandler
    startup_stm32f407xx.o(RESET) refers to usart.o(i.UART5_IRQHandler) for UART5_IRQHandler
    startup_stm32f407xx.o(RESET) refers to tim.o(i.TIM6_DAC_IRQHandler) for TIM6_DAC_IRQHandler
    startup_stm32f407xx.o(RESET) refers to tim.o(i.TIM7_IRQHandler) for TIM7_IRQHandler
    startup_stm32f407xx.o(RESET) refers to usart.o(i.USART6_IRQHandler) for USART6_IRQHandler
    startup_stm32f407xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    gpio.o(i.EXTI0_IRQHandler) refers to gpio.o(.bss) for .bss
    gpio.o(i.EXTI15_10_IRQHandler) refers to gpio.o(.bss) for .bss
    gpio.o(i.EXTI1_IRQHandler) refers to gpio.o(.bss) for .bss
    gpio.o(i.EXTI2_IRQHandler) refers to gpio.o(.bss) for .bss
    gpio.o(i.EXTI3_IRQHandler) refers to gpio.o(.bss) for .bss
    gpio.o(i.EXTI4_IRQHandler) refers to gpio.o(.bss) for .bss
    gpio.o(i.EXTI9_5_IRQHandler) refers to gpio.o(.bss) for .bss
    gpio.o(i.gpio_af_set) refers to gpio.o(.constdata) for .constdata
    gpio.o(i.gpio_ex_callback_deregister) refers to gpio.o(.bss) for .bss
    gpio.o(i.gpio_ex_callback_register) refers to sys.o(i.sys_nvic_init) for sys_nvic_init
    gpio.o(i.gpio_ex_callback_register) refers to gpio.o(.bss) for .bss
    gpio.o(i.gpio_nvic_ex_config) refers to gpio.o(i.gpio_pin_init) for gpio_pin_init
    gpio.o(i.gpio_nvic_ex_config) refers to sys.o(i.sys_nvic_init) for sys_nvic_init
    gpio.o(i.gpio_nvic_ex_config) refers to gpio.o(.constdata) for .constdata
    gpio.o(i.gpio_nvic_ex_config) refers to gpio.o(.bss) for .bss
    gpio.o(i.gpio_pin_init) refers to gpio.o(.constdata) for .constdata
    gpio.o(i.gpio_read_pin) refers to gpio.o(.constdata) for .constdata
    gpio.o(i.gpio_toggle_pin) refers to gpio.o(.constdata) for .constdata
    gpio.o(i.gpio_write_pin) refers to gpio.o(.constdata) for .constdata
    sys.o(i.HardFault_Handler) refers to debug.o(i.my_printf) for my_printf
    sys.o(i.PendSV_Handler) refers to port.o(.emb_text) for xPortPendSVHandler
    sys.o(i.SVC_Handler) refers to port.o(.emb_text) for vPortSVCHandler
    sys.o(i.SysTick_Handler) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    sys.o(i.SysTick_Handler) refers to port.o(i.xPortSysTickHandler) for xPortSysTickHandler
    sys.o(i.SysTick_Handler) refers to sys.o(.data) for .data
    sys.o(i.sys_check_rst) refers to debug.o(i.debug_printf) for debug_printf
    sys.o(i.sys_get_tick) refers to sys.o(.data) for .data
    sys.o(i.sys_stm32_clock_init) refers to sys.o(i.sys_clock_set) for sys_clock_set
    delay.o(i.delay_init) refers to delay.o(.data) for .data
    delay.o(i.delay_ms) refers to delay.o(.data) for .data
    delay.o(i.delay_us) refers to delay.o(.data) for .data
    usart.o(i.UART4_IRQHandler) refers to usart.o(.bss) for .bss
    usart.o(i.UART5_IRQHandler) refers to usart.o(.bss) for .bss
    usart.o(i.USART1_IRQHandler) refers to usart.o(.bss) for .bss
    usart.o(i.USART2_IRQHandler) refers to usart.o(.bss) for .bss
    usart.o(i.USART3_IRQHandler) refers to usart.o(.bss) for .bss
    usart.o(i.USART6_IRQHandler) refers to usart.o(.bss) for .bss
    usart.o(i.usart_init) refers to sys.o(i.sys_nvic_init) for sys_nvic_init
    usart.o(i.usart_init) refers to gpio.o(i.gpio_pin_init) for gpio_pin_init
    usart.o(i.usart_init) refers to gpio.o(i.gpio_af_set) for gpio_af_set
    usart.o(i.usart_iqr_callback_deregister) refers to usart.o(.bss) for .bss
    usart.o(i.usart_iqr_callback_register) refers to usart.o(.bss) for .bss
    i2c.o(i.i2c_ack) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    i2c.o(i.i2c_ack) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.i2c_ack) refers to i2c.o(.data) for .data
    i2c.o(i.i2c_delay) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.i2c_init) refers to gpio.o(i.gpio_pin_init) for gpio_pin_init
    i2c.o(i.i2c_init) refers to i2c.o(.data) for .data
    i2c.o(i.i2c_nack) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    i2c.o(i.i2c_nack) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.i2c_nack) refers to i2c.o(.data) for .data
    i2c.o(i.i2c_read_byte) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    i2c.o(i.i2c_read_byte) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.i2c_read_byte) refers to gpio.o(i.gpio_read_pin) for gpio_read_pin
    i2c.o(i.i2c_read_byte) refers to i2c.o(.data) for .data
    i2c.o(i.i2c_read_data) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    i2c.o(i.i2c_read_data) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.i2c_read_data) refers to i2c.o(i.i2c_send_byte) for i2c_send_byte
    i2c.o(i.i2c_read_data) refers to i2c.o(i.i2c_wait_ack) for i2c_wait_ack
    i2c.o(i.i2c_read_data) refers to i2c.o(i.i2c_stop) for i2c_stop
    i2c.o(i.i2c_read_data) refers to debug.o(i.debug_printf) for debug_printf
    i2c.o(i.i2c_read_data) refers to gpio.o(i.gpio_read_pin) for gpio_read_pin
    i2c.o(i.i2c_read_data) refers to i2c.o(.data) for .data
    i2c.o(i.i2c_send_byte) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    i2c.o(i.i2c_send_byte) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.i2c_send_byte) refers to i2c.o(.data) for .data
    i2c.o(i.i2c_start) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    i2c.o(i.i2c_start) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.i2c_start) refers to i2c.o(.data) for .data
    i2c.o(i.i2c_stop) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    i2c.o(i.i2c_stop) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.i2c_stop) refers to i2c.o(.data) for .data
    i2c.o(i.i2c_wait_ack) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    i2c.o(i.i2c_wait_ack) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.i2c_wait_ack) refers to gpio.o(i.gpio_read_pin) for gpio_read_pin
    i2c.o(i.i2c_wait_ack) refers to i2c.o(.data) for .data
    i2c.o(i.i2c_write_data) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    i2c.o(i.i2c_write_data) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.i2c_write_data) refers to i2c.o(i.i2c_send_byte) for i2c_send_byte
    i2c.o(i.i2c_write_data) refers to i2c.o(i.i2c_wait_ack) for i2c_wait_ack
    i2c.o(i.i2c_write_data) refers to i2c.o(i.i2c_stop) for i2c_stop
    i2c.o(i.i2c_write_data) refers to i2c.o(.data) for .data
    dma.o(i.dma_basic_config) refers to delay.o(i.delay_ms) for delay_ms
    spi.o(i.spi_pin_init) refers to gpio.o(i.gpio_pin_init) for gpio_pin_init
    spi.o(i.spi_pin_init) refers to gpio.o(i.gpio_af_set) for gpio_af_set
    adc.o(i.adc_get_value) refers to adc.o(.data) for .data
    adc.o(i.adc_get_value) refers to adc.o(.bss) for .bss
    adc.o(i.adc_pin_init) refers to debug.o(i.debug_error) for debug_error
    adc.o(i.adc_pin_init) refers to dma.o(i.dma_basic_config) for dma_basic_config
    adc.o(i.adc_pin_init) refers to gpio.o(i.gpio_pin_init) for gpio_pin_init
    adc.o(i.adc_pin_init) refers to dma.o(i.dma_enable) for dma_enable
    adc.o(i.adc_pin_init) refers to adc.o(.constdata) for .constdata
    adc.o(i.adc_pin_init) refers to adc.o(.bss) for .bss
    adc.o(i.adc_pin_init) refers to adc.o(.data) for .data
    tim.o(i.TIM1_BRK_TIM9_IRQHandler) refers to tim.o(i.tim_cap_irq_callback) for tim_cap_irq_callback
    tim.o(i.TIM1_BRK_TIM9_IRQHandler) refers to tim.o(.bss) for .bss
    tim.o(i.TIM1_CC_IRQHandler) refers to tim.o(i.tim_cap_irq_callback) for tim_cap_irq_callback
    tim.o(i.TIM1_CC_IRQHandler) refers to tim.o(.bss) for .bss
    tim.o(i.TIM1_TRG_COM_TIM11_IRQHandler) refers to tim.o(i.tim_cap_irq_callback) for tim_cap_irq_callback
    tim.o(i.TIM1_TRG_COM_TIM11_IRQHandler) refers to tim.o(.bss) for .bss
    tim.o(i.TIM1_UP_TIM10_IRQHandler) refers to tim.o(i.tim_cap_irq_callback) for tim_cap_irq_callback
    tim.o(i.TIM1_UP_TIM10_IRQHandler) refers to tim.o(.bss) for .bss
    tim.o(i.TIM2_IRQHandler) refers to tim.o(i.tim_cap_irq_callback) for tim_cap_irq_callback
    tim.o(i.TIM2_IRQHandler) refers to tim.o(.bss) for .bss
    tim.o(i.TIM3_IRQHandler) refers to tim.o(i.tim_cap_irq_callback) for tim_cap_irq_callback
    tim.o(i.TIM3_IRQHandler) refers to tim.o(.bss) for .bss
    tim.o(i.TIM5_IRQHandler) refers to tim.o(i.tim_cap_irq_callback) for tim_cap_irq_callback
    tim.o(i.TIM5_IRQHandler) refers to tim.o(.bss) for .bss
    tim.o(i.TIM6_DAC_IRQHandler) refers to tim.o(.data) for .data
    tim.o(i.TIM6_DAC_IRQHandler) refers to tim.o(.bss) for .bss
    tim.o(i.TIM7_IRQHandler) refers to tim.o(i.tim_cap_irq_callback) for tim_cap_irq_callback
    tim.o(i.TIM7_IRQHandler) refers to tim.o(.bss) for .bss
    tim.o(i.TIM8_BRK_TIM12_IRQHandler) refers to tim.o(i.tim_cap_irq_callback) for tim_cap_irq_callback
    tim.o(i.TIM8_BRK_TIM12_IRQHandler) refers to tim.o(.bss) for .bss
    tim.o(i.TIM8_CC_IRQHandler) refers to tim.o(i.tim_cap_irq_callback) for tim_cap_irq_callback
    tim.o(i.TIM8_CC_IRQHandler) refers to tim.o(.bss) for .bss
    tim.o(i.TIM8_TRG_COM_TIM14_IRQHandler) refers to tim.o(i.tim_cap_irq_callback) for tim_cap_irq_callback
    tim.o(i.TIM8_TRG_COM_TIM14_IRQHandler) refers to tim.o(.bss) for .bss
    tim.o(i.TIM8_UP_TIM13_IRQHandler) refers to tim.o(i.tim_cap_irq_callback) for tim_cap_irq_callback
    tim.o(i.TIM8_UP_TIM13_IRQHandler) refers to tim.o(.bss) for .bss
    tim.o(i.btimx_int_init) refers to sys.o(i.sys_nvic_init) for sys_nvic_init
    tim.o(i.tim_cap_get_value) refers to tim.o(.bss) for .bss
    tim.o(i.tim_cap_irq_callback) refers to tim.o(.bss) for .bss
    tim.o(i.tim_cap_pin_init) refers to gpio.o(i.gpio_pin_init) for gpio_pin_init
    tim.o(i.tim_cap_pin_init) refers to gpio.o(i.gpio_af_set) for gpio_af_set
    tim.o(i.tim_cap_pin_init) refers to sys.o(i.sys_nvic_init) for sys_nvic_init
    tim.o(i.tim_cap_pin_init) refers to tim.o(.bss) for .bss
    tim.o(i.tim_cnt_pin_init) refers to gpio.o(i.gpio_pin_init) for gpio_pin_init
    tim.o(i.tim_cnt_pin_init) refers to gpio.o(i.gpio_af_set) for gpio_af_set
    tim.o(i.tim_int_callback_deregister) refers to tim.o(.data) for .data
    tim.o(i.tim_int_callback_deregister) refers to tim.o(.bss) for .bss
    tim.o(i.tim_int_callback_register) refers to sys.o(i.sys_nvic_init) for sys_nvic_init
    tim.o(i.tim_int_callback_register) refers to debug.o(i.debug_error) for debug_error
    tim.o(i.tim_int_callback_register) refers to tim.o(.data) for .data
    tim.o(i.tim_int_callback_register) refers to tim.o(.constdata) for .constdata
    tim.o(i.tim_int_callback_register) refers to tim.o(.bss) for .bss
    tim.o(i.tim_pwm_pin_init) refers to gpio.o(i.gpio_pin_init) for gpio_pin_init
    tim.o(i.tim_pwm_pin_init) refers to gpio.o(i.gpio_af_set) for gpio_af_set
    led.o(i.led_init) refers to gpio.o(i.gpio_pin_init) for gpio_pin_init
    led.o(i.led_init) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    led.o(i.led_init) refers to led.o(.constdata) for .constdata
    led.o(i.led_set) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    led.o(i.led_set) refers to led.o(.constdata) for .constdata
    led.o(i.led_toggle) refers to gpio.o(i.gpio_toggle_pin) for gpio_toggle_pin
    led.o(i.led_toggle) refers to led.o(.constdata) for .constdata
    debug.o(i.debug_error) refers to memseta.o(.text) for __aeabi_memclr
    debug.o(i.debug_error) refers to debug.o(i.get_debug_time) for get_debug_time
    debug.o(i.debug_error) refers to printfa.o(i.__0snprintf) for __2snprintf
    debug.o(i.debug_error) refers to strlen.o(.text) for strlen
    debug.o(i.debug_error) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    debug.o(i.debug_error) refers to usart.o(i.usart_send_data) for usart_send_data
    debug.o(i.debug_error) refers to debug.o(.bss) for .bss
    debug.o(i.debug_init) refers to usart.o(i.usart_init) for usart_init
    debug.o(i.debug_init) refers to memseta.o(.text) for __aeabi_memclr
    debug.o(i.debug_init) refers to strlen.o(.text) for strlen
    debug.o(i.debug_init) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    debug.o(i.debug_init) refers to usart.o(i.usart_send_data) for usart_send_data
    debug.o(i.debug_init) refers to debug.o(.bss) for .bss
    debug.o(i.debug_message) refers to memseta.o(.text) for __aeabi_memclr
    debug.o(i.debug_message) refers to debug.o(i.get_debug_time) for get_debug_time
    debug.o(i.debug_message) refers to printfa.o(i.__0snprintf) for __2snprintf
    debug.o(i.debug_message) refers to strlen.o(.text) for strlen
    debug.o(i.debug_message) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    debug.o(i.debug_message) refers to usart.o(i.usart_send_data) for usart_send_data
    debug.o(i.debug_message) refers to debug.o(.bss) for .bss
    debug.o(i.debug_printf) refers to memseta.o(.text) for __aeabi_memclr
    debug.o(i.debug_printf) refers to debug.o(i.get_debug_time) for get_debug_time
    debug.o(i.debug_printf) refers to printfa.o(i.__0snprintf) for __2snprintf
    debug.o(i.debug_printf) refers to strlen.o(.text) for strlen
    debug.o(i.debug_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    debug.o(i.debug_printf) refers to usart.o(i.usart_send_data) for usart_send_data
    debug.o(i.debug_printf) refers to debug.o(.bss) for .bss
    debug.o(i.debug_send_data) refers to usart.o(i.usart_send_data) for usart_send_data
    debug.o(i.debug_warning) refers to memseta.o(.text) for __aeabi_memclr
    debug.o(i.debug_warning) refers to debug.o(i.get_debug_time) for get_debug_time
    debug.o(i.debug_warning) refers to printfa.o(i.__0snprintf) for __2snprintf
    debug.o(i.debug_warning) refers to strlen.o(.text) for strlen
    debug.o(i.debug_warning) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    debug.o(i.debug_warning) refers to usart.o(i.usart_send_data) for usart_send_data
    debug.o(i.debug_warning) refers to debug.o(.bss) for .bss
    debug.o(i.get_debug_time) refers to sys.o(i.sys_get_tick) for sys_get_tick
    debug.o(i.get_debug_time) refers to printfa.o(i.__0snprintf) for __2snprintf
    debug.o(i.get_debug_time) refers to debug.o(.bss) for .bss
    debug.o(i.my_printf) refers to memseta.o(.text) for __aeabi_memclr
    debug.o(i.my_printf) refers to strlen.o(.text) for strlen
    debug.o(i.my_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    debug.o(i.my_printf) refers to usart.o(i.usart_send_data) for usart_send_data
    debug.o(i.my_printf) refers to debug.o(.bss) for .bss
    oled.o(i.OLED_Clear) refers to memseta.o(.text) for __aeabi_memclr
    oled.o(i.OLED_Clear) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_ClearArea) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_DrawArc) refers to oled.o(i.OLED_IsInAngle) for OLED_IsInAngle
    oled.o(i.OLED_DrawArc) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawCircle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawEllipse) refers to dflti.o(.text) for __aeabi_i2d
    oled.o(i.OLED_DrawEllipse) refers to dadd.o(.text) for __aeabi_dadd
    oled.o(i.OLED_DrawEllipse) refers to dmul.o(.text) for __aeabi_dmul
    oled.o(i.OLED_DrawEllipse) refers to d2f.o(.text) for __aeabi_d2f
    oled.o(i.OLED_DrawEllipse) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawEllipse) refers to cdcmple.o(.text) for __aeabi_cdcmple
    oled.o(i.OLED_DrawLine) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawPoint) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_DrawRectangle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_DrawLine) for OLED_DrawLine
    oled.o(i.OLED_GPIO_Init) refers to i2c.o(i.i2c_init) for i2c_init
    oled.o(i.OLED_GetPoint) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_Init) refers to i2c.o(i.i2c_init) for i2c_init
    oled.o(i.OLED_Init) refers to i2c.o(i.i2c_write_data) for i2c_write_data
    oled.o(i.OLED_Init) refers to memseta.o(.text) for __aeabi_memclr
    oled.o(i.OLED_Init) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_IsInAngle) refers to dflti.o(.text) for __aeabi_i2d
    oled.o(i.OLED_IsInAngle) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    oled.o(i.OLED_IsInAngle) refers to ddiv.o(.text) for __aeabi_ddiv
    oled.o(i.OLED_IsInAngle) refers to dmul.o(.text) for __aeabi_dmul
    oled.o(i.OLED_IsInAngle) refers to dfixi.o(.text) for __aeabi_d2iz
    oled.o(i.OLED_Printf) refers to printfa.o(i.__0vsprintf) for vsprintf
    oled.o(i.OLED_Printf) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    oled.o(i.OLED_Reverse) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_ReverseArea) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_SetCursor) refers to i2c.o(i.i2c_write_data) for i2c_write_data
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_ShowImage) for OLED_ShowImage
    oled.o(i.OLED_ShowChar) refers to oled_data.o(.constdata) for OLED_F6x8
    oled.o(i.OLED_ShowChar) refers to oled_data.o(.constdata) for OLED_F8x16
    oled.o(i.OLED_ShowFloatNum) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    oled.o(i.OLED_ShowFloatNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowFloatNum) refers to dfixui.o(.text) for __aeabi_d2uiz
    oled.o(i.OLED_ShowFloatNum) refers to dfltui.o(.text) for __aeabi_ui2d
    oled.o(i.OLED_ShowFloatNum) refers to dadd.o(.text) for __aeabi_drsub
    oled.o(i.OLED_ShowFloatNum) refers to dmul.o(.text) for __aeabi_dmul
    oled.o(i.OLED_ShowFloatNum) refers to round.o(i.__hardfp_round) for __hardfp_round
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowImage) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowString) refers to strcmp.o(.text) for strcmp
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowImage) for OLED_ShowImage
    oled.o(i.OLED_ShowString) refers to oled_data.o(.constdata) for OLED_CF16x16
    oled.o(i.OLED_Update) refers to i2c.o(i.i2c_write_data) for i2c_write_data
    oled.o(i.OLED_Update) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_UpdateArea) refers to i2c.o(i.i2c_write_data) for i2c_write_data
    oled.o(i.OLED_UpdateArea) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_WriteCommand) refers to i2c.o(i.i2c_write_data) for i2c_write_data
    oled.o(i.OLED_WriteData) refers to i2c.o(i.i2c_write_data) for i2c_write_data
    gpio_key.o(i.gpio_key_get_state) refers to gpio_key.o(.data) for .data
    gpio_key.o(i.gpio_key_init) refers to gpio.o(i.gpio_pin_init) for gpio_pin_init
    gpio_key.o(i.gpio_key_init) refers to gpio_key.o(.constdata) for .constdata
    gpio_key.o(i.gpio_key_read) refers to gpio.o(i.gpio_read_pin) for gpio_read_pin
    gpio_key.o(i.gpio_key_read) refers to gpio_key.o(.constdata) for .constdata
    gpio_key.o(i.gpio_key_scan) refers to gpio.o(i.gpio_read_pin) for gpio_read_pin
    gpio_key.o(i.gpio_key_scan) refers to gpio_key.o(.constdata) for .constdata
    gpio_key.o(i.gpio_key_scan) refers to gpio_key.o(.data) for .data
    key.o(i.key_get_value) refers to gpio_key.o(i.gpio_key_get_state) for gpio_key_get_state
    key.o(i.key_init) refers to gpio_key.o(i.gpio_key_init) for gpio_key_init
    key.o(i.key_init) refers to tim.o(i.tim_int_callback_register) for tim_int_callback_register
    key.o(i.key_init) refers to key.o(i.key_scan) for key_scan
    key.o(i.key_scan) refers to gpio_key.o(i.gpio_key_scan) for gpio_key_scan
    tb6612.o(i.tb6612_init) refers to gpio.o(i.gpio_pin_init) for gpio_pin_init
    tb6612.o(i.tb6612_init) refers to tim.o(i.tim_pwm_pin_init) for tim_pwm_pin_init
    tb6612.o(i.tb6612_init) refers to tim.o(i.tim_pwm_set_ccr) for tim_pwm_set_ccr
    tb6612.o(i.tb6612_init) refers to tb6612.o(.constdata) for .constdata
    tb6612.o(i.tb6612_set_direction) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    tb6612.o(i.tb6612_set_direction) refers to tb6612.o(.constdata) for .constdata
    encoder.o(i.encoder_get_value) refers to tim.o(i.tim_cnt_get_value) for tim_cnt_get_value
    encoder.o(i.encoder_get_value) refers to tim.o(i.tim_cnt_set_value) for tim_cnt_set_value
    encoder.o(i.encoder_get_value) refers to encoder.o(.constdata) for .constdata
    encoder.o(i.encoder_init) refers to tim.o(i.tim_cnt_pin_init) for tim_cnt_pin_init
    encoder.o(i.encoder_init) refers to tim.o(i.tim_cnt_set_value) for tim_cnt_set_value
    encoder.o(i.encoder_init) refers to encoder.o(.constdata) for .constdata
    track.o(i.rrrr) refers to i2c.o(i.i2c_start) for i2c_start
    track.o(i.rrrr) refers to i2c.o(i.i2c_send_byte) for i2c_send_byte
    track.o(i.rrrr) refers to i2c.o(i.i2c_wait_ack) for i2c_wait_ack
    track.o(i.rrrr) refers to i2c.o(i.i2c_read_byte) for i2c_read_byte
    track.o(i.rrrr) refers to i2c.o(i.i2c_stop) for i2c_stop
    track.o(i.track_get_sensor_data) refers to track.o(.data) for .data
    track.o(i.track_init) refers to i2c.o(i.i2c_init) for i2c_init
    track.o(i.track_read_sensors) refers to i2c.o(i.i2c_read_data) for i2c_read_data
    track.o(i.track_read_sensors) refers to track.o(.data) for .data
    main.o(i.app_init) refers to app_state.o(i.app_state_task_start) for app_state_task_start
    main.o(i.app_init) refers to debug.o(i.debug_printf) for debug_printf
    main.o(i.app_init) refers to app_key.o(i.app_key_task_start) for app_key_task_start
    main.o(i.app_init) refers to app_ui.o(i.app_ui_task_start) for app_ui_task_start
    main.o(i.app_init) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    main.o(i.app_init) refers to main.o(i.task_test) for task_test
    main.o(i.board_init) refers to led.o(i.led_init) for led_init
    main.o(i.board_init) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.board_init) refers to key.o(i.key_init) for key_init
    main.o(i.led_test) refers to led.o(i.led_toggle) for led_toggle
    main.o(i.main) refers to sys.o(i.sys_stm32_clock_init) for sys_stm32_clock_init
    main.o(i.main) refers to debug.o(i.debug_init) for debug_init
    main.o(i.main) refers to delay.o(i.delay_init) for delay_init
    main.o(i.main) refers to sys.o(i.sys_check_rst) for sys_check_rst
    main.o(i.main) refers to malloc.o(i.my_mem_init) for my_mem_init
    main.o(i.main) refers to led.o(i.led_init) for led_init
    main.o(i.main) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.main) refers to key.o(i.key_init) for key_init
    main.o(i.main) refers to app_state.o(i.app_state_task_start) for app_state_task_start
    main.o(i.main) refers to debug.o(i.debug_printf) for debug_printf
    main.o(i.main) refers to app_key.o(i.app_key_task_start) for app_key_task_start
    main.o(i.main) refers to app_ui.o(i.app_ui_task_start) for app_ui_task_start
    main.o(i.main) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    main.o(i.main) refers to tasks.o(i.vTaskStartScheduler) for vTaskStartScheduler
    main.o(i.main) refers to tim.o(i.tim_int_callback_register) for tim_int_callback_register
    main.o(i.main) refers to tim.o(i.tim_pwm_pin_init) for tim_pwm_pin_init
    main.o(i.main) refers to tim.o(i.tim_pwm_set_ccr) for tim_pwm_set_ccr
    main.o(i.main) refers to key.o(i.key_get_value) for key_get_value
    main.o(i.main) refers to tim.o(i.tim_int_callback_deregister) for tim_int_callback_deregister
    main.o(i.main) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.main) refers to usmart_config.o(.data) for usmart_dev
    main.o(i.main) refers to main.o(i.app_init) for i.app_init
    main.o(i.main) refers to main.o(i.task_test) for task_test
    main.o(i.sys_init) refers to sys.o(i.sys_stm32_clock_init) for sys_stm32_clock_init
    main.o(i.sys_init) refers to debug.o(i.debug_init) for debug_init
    main.o(i.sys_init) refers to delay.o(i.delay_init) for delay_init
    main.o(i.sys_init) refers to sys.o(i.sys_check_rst) for sys_check_rst
    main.o(i.sys_init) refers to malloc.o(i.my_mem_init) for my_mem_init
    main.o(i.sys_init) refers to usmart_config.o(.data) for usmart_dev
    main.o(i.task_test) refers to adc.o(i.adc_pin_init) for adc_pin_init
    main.o(i.task_test) refers to gpio.o(i.gpio_nvic_ex_config) for gpio_nvic_ex_config
    main.o(i.task_test) refers to encoder.o(i.encoder_init) for encoder_init
    main.o(i.task_test) refers to track.o(i.track_init) for track_init
    main.o(i.task_test) refers to track.o(i.track_read_sensors) for track_read_sensors
    main.o(i.task_test) refers to track.o(i.track_get_sensor_data) for track_get_sensor_data
    main.o(i.task_test) refers to debug.o(i.debug_printf) for debug_printf
    main.o(i.task_test) refers to tasks.o(i.vTaskDelay) for vTaskDelay
    main.o(i.task_test) refers to main.o(i.led_test) for led_test
    app_key.o(i.app_key_task) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    app_key.o(i.app_key_task) refers to tasks.o(i.xTaskDelayUntil) for xTaskDelayUntil
    app_key.o(i.app_key_task) refers to key.o(i.key_get_value) for key_get_value
    app_key.o(i.app_key_task) refers to debug.o(i.debug_printf) for debug_printf
    app_key.o(i.app_key_task) refers to sys.o(i.sys_soft_reset) for sys_soft_reset
    app_key.o(i.app_key_task) refers to tasks.o(i.vTaskDelete) for vTaskDelete
    app_key.o(i.app_key_task) refers to app_key.o(.data) for .data
    app_key.o(i.app_key_task_start) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    app_key.o(i.app_key_task_start) refers to app_key.o(.data) for .data
    app_key.o(i.app_key_task_start) refers to app_key.o(i.app_key_task) for app_key_task
    app_key.o(i.app_key_task_stop) refers to app_key.o(.data) for .data
    app_state.o(i.app_state_task) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    app_state.o(i.app_state_task) refers to led.o(i.led_toggle) for led_toggle
    app_state.o(i.app_state_task) refers to tasks.o(i.xTaskDelayUntil) for xTaskDelayUntil
    app_state.o(i.app_state_task) refers to debug.o(i.debug_printf) for debug_printf
    app_state.o(i.app_state_task) refers to malloc.o(i.my_mem_perused) for my_mem_perused
    app_state.o(i.app_state_task) refers to heap_4.o(i.xPortGetFreeHeapSize) for xPortGetFreeHeapSize
    app_state.o(i.app_state_task) refers to tasks.o(i.vTaskListTasks) for vTaskListTasks
    app_state.o(i.app_state_task) refers to debug.o(i.my_printf) for my_printf
    app_state.o(i.app_state_task) refers to tasks.o(i.vTaskDelete) for vTaskDelete
    app_state.o(i.app_state_task) refers to app_state.o(.data) for .data
    app_state.o(i.app_state_task) refers to app_state.o(.bss) for .bss
    app_state.o(i.app_state_task_start) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    app_state.o(i.app_state_task_start) refers to app_state.o(.data) for .data
    app_state.o(i.app_state_task_start) refers to app_state.o(i.app_state_task) for app_state_task
    app_state.o(i.app_state_task_stop) refers to app_state.o(.data) for .data
    app_ui.o(i.app_ui_task) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    app_ui.o(i.app_ui_task) refers to oled.o(i.OLED_Update) for OLED_Update
    app_ui.o(i.app_ui_task) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    app_ui.o(i.app_ui_task) refers to oled.o(i.OLED_Printf) for OLED_Printf
    app_ui.o(i.app_ui_task) refers to tasks.o(i.xTaskDelayUntil) for xTaskDelayUntil
    app_ui.o(i.app_ui_task) refers to tasks.o(i.vTaskDelete) for vTaskDelete
    app_ui.o(i.app_ui_task) refers to app_ui.o(.data) for .data
    app_ui.o(i.app_ui_task_start) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    app_ui.o(i.app_ui_task_start) refers to app_ui.o(.data) for .data
    app_ui.o(i.app_ui_task_start) refers to app_ui.o(i.app_ui_task) for app_ui_task
    app_ui.o(i.app_ui_task_stop) refers to app_ui.o(.data) for .data
    malloc.o(i.my_mem_init) refers to malloc.o(.data) for .data
    malloc.o(i.my_mem_init) refers to malloc.o(.constdata) for .constdata
    malloc.o(i.my_mem_malloc) refers to malloc.o(.data) for .data
    malloc.o(i.my_mem_malloc) refers to malloc.o(.constdata) for .constdata
    malloc.o(i.my_mem_perused) refers to malloc.o(.constdata) for .constdata
    malloc.o(i.my_mem_perused) refers to malloc.o(.data) for .data
    malloc.o(i.myfree) refers to malloc.o(.data) for .data
    malloc.o(i.myfree) refers to malloc.o(.constdata) for .constdata
    malloc.o(i.mymalloc) refers to malloc.o(i.my_mem_malloc) for my_mem_malloc
    malloc.o(i.mymalloc) refers to malloc.o(.data) for .data
    malloc.o(i.myrealloc) refers to malloc.o(i.my_mem_malloc) for my_mem_malloc
    malloc.o(i.myrealloc) refers to malloc.o(.data) for .data
    malloc.o(i.myrealloc) refers to malloc.o(.constdata) for .constdata
    malloc.o(.data) refers to malloc.o(i.my_mem_init) for my_mem_init
    malloc.o(.data) refers to malloc.o(i.my_mem_perused) for my_mem_perused
    malloc.o(.data) refers to malloc.o(.bss) for mem1base
    malloc.o(.data) refers to malloc.o(.bss) for mem1mapbase
    usmart.o(i.usmart_cmd_rec) refers to usmart_str.o(i.usmart_get_fname) for usmart_get_fname
    usmart.o(i.usmart_cmd_rec) refers to usmart_str.o(i.usmart_strcmp) for usmart_strcmp
    usmart.o(i.usmart_cmd_rec) refers to usmart_str.o(i.usmart_get_fparam) for usmart_get_fparam
    usmart.o(i.usmart_cmd_rec) refers to usmart_config.o(.data) for usmart_dev
    usmart.o(i.usmart_exe) refers to usmart_str.o(i.usmart_get_fname) for usmart_get_fname
    usmart.o(i.usmart_exe) refers to debug.o(i.my_printf) for my_printf
    usmart.o(i.usmart_exe) refers to usmart_str.o(i.usmart_get_parmpos) for usmart_get_parmpos
    usmart.o(i.usmart_exe) refers to usmart_port.o(i.usmart_timx_reset_time) for usmart_timx_reset_time
    usmart.o(i.usmart_exe) refers to usmart_port.o(i.usmart_timx_get_time) for usmart_timx_get_time
    usmart.o(i.usmart_exe) refers to usmart_config.o(.data) for usmart_dev
    usmart.o(i.usmart_init) refers to usmart_port.o(i.usmart_timx_init) for usmart_timx_init
    usmart.o(i.usmart_init) refers to usart.o(i.usart_iqr_callback_register) for usart_iqr_callback_register
    usmart.o(i.usmart_init) refers to usmart_config.o(.data) for usmart_dev
    usmart.o(i.usmart_init) refers to usmart_port.o(i.usmart_recv_func) for usmart_recv_func
    usmart.o(i.usmart_scan) refers to usmart_port.o(i.usmart_get_input_string) for usmart_get_input_string
    usmart.o(i.usmart_scan) refers to usmart.o(i.usmart_sys_cmd_exe) for usmart_sys_cmd_exe
    usmart.o(i.usmart_scan) refers to debug.o(i.my_printf) for my_printf
    usmart.o(i.usmart_scan) refers to usmart_config.o(.data) for usmart_dev
    usmart.o(i.usmart_sys_cmd_exe) refers to usmart_str.o(i.usmart_get_cmdname) for usmart_get_cmdname
    usmart.o(i.usmart_sys_cmd_exe) refers to usmart_str.o(i.usmart_strcmp) for usmart_strcmp
    usmart.o(i.usmart_sys_cmd_exe) refers to debug.o(i.my_printf) for my_printf
    usmart.o(i.usmart_sys_cmd_exe) refers to usmart.o(.data) for .data
    usmart.o(i.usmart_sys_cmd_exe) refers to usmart_config.o(.data) for usmart_dev
    usmart.o(i.usmart_sys_cmd_exe) refers to usmart_str.o(i.usmart_get_fname) for usmart_get_fname
    usmart.o(i.usmart_sys_cmd_exe) refers to usmart_str.o(i.usmart_get_aparm) for usmart_get_aparm
    usmart.o(i.usmart_sys_cmd_exe) refers to usmart_str.o(i.usmart_str2num) for usmart_str2num
    usmart.o(.data) refers to usmart.o(.conststring) for .conststring
    usmart_config.o(.data) refers to usmart.o(i.read_addr) for read_addr
    usmart_config.o(.data) refers to usmart_config.o(.conststring) for .conststring
    usmart_config.o(.data) refers to usmart.o(i.write_addr) for write_addr
    usmart_config.o(.data) refers to delay.o(i.delay_ms) for delay_ms
    usmart_config.o(.data) refers to delay.o(i.delay_us) for delay_us
    usmart_config.o(.data) refers to usmart_port.o(i.usmart_get_input_string) for usmart_get_input_string
    usmart_config.o(.data) refers to usmart_config.o(.data) for usmart_nametab
    usmart_config.o(.data) refers to usmart.o(i.usmart_init) for usmart_init
    usmart_config.o(.data) refers to usmart.o(i.usmart_cmd_rec) for usmart_cmd_rec
    usmart_config.o(.data) refers to usmart.o(i.usmart_exe) for usmart_exe
    usmart_config.o(.data) refers to usmart.o(i.usmart_scan) for usmart_scan
    usmart_port.o(i.TIM4_IRQHandler) refers to usmart_config.o(.data) for usmart_dev
    usmart_port.o(i.usmart_get_input_string) refers to usmart_port.o(.data) for .data
    usmart_port.o(i.usmart_get_input_string) refers to usmart_port.o(.bss) for .bss
    usmart_port.o(i.usmart_recv_func) refers to usmart_port.o(.data) for .data
    usmart_port.o(i.usmart_recv_func) refers to usmart_port.o(.bss) for .bss
    usmart_port.o(i.usmart_timx_get_time) refers to usmart_config.o(.data) for usmart_dev
    usmart_port.o(i.usmart_timx_init) refers to sys.o(i.sys_nvic_init) for sys_nvic_init
    usmart_port.o(i.usmart_timx_reset_time) refers to usmart_config.o(.data) for usmart_dev
    usmart_str.o(i.usmart_get_fparam) refers to usmart_str.o(i.__ARM_common_memclr4_10) for __ARM_common_memclr4_10
    usmart_str.o(i.usmart_get_fparam) refers to usmart_config.o(.data) for usmart_dev
    usmart_str.o(i.usmart_get_parmpos) refers to usmart_config.o(.data) for usmart_dev
    event_groups.o(i.vEventGroupClearBitsCallback) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.vEventGroupClearBitsCallback) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.vEventGroupDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    event_groups.o(i.vEventGroupSetBitsCallback) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.vEventGroupSetBitsCallback) refers to tasks.o(i.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(i.vEventGroupSetBitsCallback) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupClearBits) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupClearBits) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    event_groups.o(i.xEventGroupCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    event_groups.o(i.xEventGroupCreate) refers to list.o(i.vListInitialise) for vListInitialise
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.vTaskPlaceOnUnorderedEventList) for vTaskPlaceOnUnorderedEventList
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.uxTaskResetEventItemValue) for uxTaskResetEventItemValue
    event_groups.o(i.xEventGroupSync) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupSync) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.vTaskPlaceOnUnorderedEventList) for vTaskPlaceOnUnorderedEventList
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.uxTaskResetEventItemValue) for uxTaskResetEventItemValue
    event_groups.o(i.xEventGroupWaitBits) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupWaitBits) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    heap_4.o(i.pvPortCalloc) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    heap_4.o(i.pvPortCalloc) refers to memseta.o(.text) for __aeabi_memclr
    heap_4.o(i.pvPortMalloc) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.pvPortMalloc) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(.data) for .data
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(.bss) for .bss
    heap_4.o(i.vPortFree) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.vPortFree) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.vPortFree) refers to heap_4.o(.data) for .data
    heap_4.o(i.vPortGetHeapStats) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.vPortGetHeapStats) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.vPortGetHeapStats) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    heap_4.o(i.vPortGetHeapStats) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    heap_4.o(i.vPortGetHeapStats) refers to heap_4.o(.data) for .data
    heap_4.o(i.vPortHeapResetState) refers to heap_4.o(.data) for .data
    heap_4.o(i.xPortGetFreeHeapSize) refers to heap_4.o(.data) for .data
    heap_4.o(i.xPortGetMinimumEverFreeHeapSize) refers to heap_4.o(.data) for .data
    port.o(.emb_text) refers to tasks.o(i.vTaskSwitchContext) for vTaskSwitchContext
    port.o(.emb_text) refers to tasks.o(.data) for pxCurrentTCB
    port.o(i.pxPortInitialiseStack) refers to port.o(i.prvTaskExitError) for prvTaskExitError
    port.o(i.vPortEnterCritical) refers to port.o(.data) for .data
    port.o(i.vPortExitCritical) refers to port.o(.data) for .data
    port.o(i.xPortStartScheduler) refers to port.o(i.vPortSetupTimerInterrupt) for vPortSetupTimerInterrupt
    port.o(i.xPortStartScheduler) refers to port.o(.emb_text) for __asm___6_port_c_39a90d8d__prvEnableVFP
    port.o(i.xPortStartScheduler) refers to port.o(.data) for .data
    port.o(i.xPortSysTickHandler) refers to tasks.o(i.xTaskIncrementTick) for xTaskIncrementTick
    queue.o(i.prvUnlockQueue) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.prvUnlockQueue) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.prvUnlockQueue) refers to tasks.o(i.vTaskMissedYield) for vTaskMissedYield
    queue.o(i.prvUnlockQueue) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.uxQueueMessagesWaiting) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.uxQueueMessagesWaiting) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.uxQueueSpacesAvailable) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.uxQueueSpacesAvailable) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.vQueueDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    queue.o(i.vQueueWaitForMessageRestricted) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.vQueueWaitForMessageRestricted) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.vQueueWaitForMessageRestricted) refers to tasks.o(i.vTaskPlaceOnEventListRestricted) for vTaskPlaceOnEventListRestricted
    queue.o(i.vQueueWaitForMessageRestricted) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueCreateMutex) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    queue.o(i.xQueueCreateMutex) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueCreateMutex) refers to list.o(i.vListInitialise) for vListInitialise
    queue.o(i.xQueueCreateMutex) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueCreateMutex) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    queue.o(i.xQueueGenericCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    queue.o(i.xQueueGenericCreate) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGenericCreate) refers to list.o(i.vListInitialise) for vListInitialise
    queue.o(i.xQueueGenericCreate) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGenericReset) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGenericReset) refers to list.o(i.vListInitialise) for vListInitialise
    queue.o(i.xQueueGenericReset) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGenericReset) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGenericSend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGenericSend) refers to memcpya.o(.text) for __aeabi_memcpy
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskPriorityDisinherit) for xTaskPriorityDisinherit
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGenericSend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueGenericSend) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueGenericSendFromISR) refers to memcpya.o(.text) for __aeabi_memcpy
    queue.o(i.xQueueGenericSendFromISR) refers to tasks.o(i.xTaskPriorityDisinherit) for xTaskPriorityDisinherit
    queue.o(i.xQueueGenericSendFromISR) refers to tasks.o(i.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    queue.o(i.xQueueGenericSendFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGiveFromISR) refers to tasks.o(i.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    queue.o(i.xQueueGiveFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueuePeek) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueuePeek) refers to memcpya.o(.text) for __aeabi_memcpy
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueuePeek) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueuePeek) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueuePeekFromISR) refers to memcpya.o(.text) for __aeabi_memcpy
    queue.o(i.xQueueReceive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueReceive) refers to memcpya.o(.text) for __aeabi_memcpy
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueReceive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueReceive) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueReceiveFromISR) refers to memcpya.o(.text) for __aeabi_memcpy
    queue.o(i.xQueueReceiveFromISR) refers to tasks.o(i.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    queue.o(i.xQueueReceiveFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueSemaphoreTake) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.pvTaskIncrementMutexHeldCount) for pvTaskIncrementMutexHeldCount
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueSemaphoreTake) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueSemaphoreTake) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskPriorityInherit) for xTaskPriorityInherit
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskPriorityDisinheritAfterTimeout) for vTaskPriorityDisinheritAfterTimeout
    tasks.o(i.eTaskGetState) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.eTaskGetState) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.eTaskGetState) refers to tasks.o(.data) for .data
    tasks.o(i.eTaskGetState) refers to tasks.o(.bss) for .bss
    tasks.o(i.pcTaskGetName) refers to tasks.o(.data) for .data
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.vListInsert) for vListInsert
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to tasks.o(.data) for .data
    tasks.o(i.prvIdleTask) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.prvIdleTask) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.prvIdleTask) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.prvIdleTask) refers to heap_4.o(i.vPortFree) for vPortFree
    tasks.o(i.prvIdleTask) refers to tasks.o(.data) for .data
    tasks.o(i.prvIdleTask) refers to tasks.o(.bss) for .bss
    tasks.o(i.prvListTasksWithinSingleList) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.prvListTasksWithinSingleList) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.prvListTasksWithinSingleList) refers to tasks.o(.data) for .data
    tasks.o(i.prvListTasksWithinSingleList) refers to tasks.o(.bss) for .bss
    tasks.o(i.pvTaskIncrementMutexHeldCount) refers to tasks.o(.data) for .data
    tasks.o(i.ulTaskGenericNotifyTake) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.ulTaskGenericNotifyTake) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.ulTaskGenericNotifyTake) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.ulTaskGenericNotifyTake) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.ulTaskGenericNotifyTake) refers to tasks.o(.data) for .data
    tasks.o(i.ulTaskGenericNotifyValueClear) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.ulTaskGenericNotifyValueClear) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.ulTaskGenericNotifyValueClear) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskGetNumberOfTasks) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(i.prvListTasksWithinSingleList) for prvListTasksWithinSingleList
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(.bss) for .bss
    tasks.o(i.uxTaskResetEventItemValue) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelay) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskDelay) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.vTaskDelay) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelete) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskDelete) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskDelete) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskDelete) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    tasks.o(i.vTaskDelete) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelete) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskEndScheduler) refers to timers.o(i.xTimerGetTimerDaemonTaskHandle) for xTimerGetTimerDaemonTaskHandle
    tasks.o(i.vTaskEndScheduler) refers to tasks.o(i.vTaskDelete) for vTaskDelete
    tasks.o(i.vTaskEndScheduler) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskEndScheduler) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskEndScheduler) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskEndScheduler) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskEndScheduler) refers to heap_4.o(i.vPortFree) for vPortFree
    tasks.o(i.vTaskEndScheduler) refers to port.o(i.vPortEndScheduler) for vPortEndScheduler
    tasks.o(i.vTaskEndScheduler) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskEndScheduler) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskGenericNotifyGiveFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskGenericNotifyGiveFromISR) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskGetInfo) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskGetInfo) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskGetInfo) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskGetInfo) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskInternalSetTimeOutState) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskListTasks) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    tasks.o(i.vTaskListTasks) refers to tasks.o(i.prvListTasksWithinSingleList) for prvListTasksWithinSingleList
    tasks.o(i.vTaskListTasks) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.vTaskListTasks) refers to strcpy.o(.text) for strcpy
    tasks.o(i.vTaskListTasks) refers to strlen.o(.text) for strlen
    tasks.o(i.vTaskListTasks) refers to printfa.o(i.__0snprintf) for __2snprintf
    tasks.o(i.vTaskListTasks) refers to heap_4.o(i.vPortFree) for vPortFree
    tasks.o(i.vTaskListTasks) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskListTasks) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskMissedYield) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPlaceOnEventList) refers to list.o(i.vListInsert) for vListInsert
    tasks.o(i.vTaskPlaceOnEventList) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnEventList) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPlaceOnEventListRestricted) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnEventListRestricted) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskResetState) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSetTimeOutState) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskSetTimeOutState) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskSetTimeOutState) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    tasks.o(i.vTaskStartScheduler) refers to timers.o(i.xTimerCreateTimerTask) for xTimerCreateTimerTask
    tasks.o(i.vTaskStartScheduler) refers to port.o(i.xPortStartScheduler) for xPortStartScheduler
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(i.prvIdleTask) for prvIdleTask
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSuspendAll) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSwitchContext) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSwitchContext) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskCatchUpTicks) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskCatchUpTicks) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskCatchUpTicks) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.xTaskCatchUpTicks) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskCheckForTimeOut) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskCheckForTimeOut) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskCheckForTimeOut) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    tasks.o(i.xTaskCreate) refers to memseta.o(.text) for __aeabi_memclr4
    tasks.o(i.xTaskCreate) refers to list.o(i.vListInitialiseItem) for vListInitialiseItem
    tasks.o(i.xTaskCreate) refers to port.o(i.pxPortInitialiseStack) for pxPortInitialiseStack
    tasks.o(i.xTaskCreate) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskCreate) refers to heap_4.o(i.vPortFree) for vPortFree
    tasks.o(i.xTaskCreate) refers to list.o(i.vListInitialise) for vListInitialise
    tasks.o(i.xTaskCreate) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskCreate) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskCreate) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskDelayUntil) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.xTaskDelayUntil) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.xTaskDelayUntil) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGenericNotify) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskGenericNotify) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskGenericNotify) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGenericNotify) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskGenericNotifyFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGenericNotifyFromISR) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskGenericNotifyStateClear) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskGenericNotifyStateClear) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskGenericNotifyStateClear) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGenericNotifyWait) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskGenericNotifyWait) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskGenericNotifyWait) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.xTaskGenericNotifyWait) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.xTaskGenericNotifyWait) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetCurrentTaskHandle) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetCurrentTaskHandleForCore) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetSchedulerState) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetTickCount) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetTickCountFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskPriorityDisinherit) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskPriorityDisinherit) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskPriorityDisinherit) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskPriorityInherit) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskPriorityInherit) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskPriorityInherit) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskRemoveFromEventList) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskRemoveFromEventList) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskResumeAll) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskResumeAll) refers to tasks.o(i.xTaskIncrementTick) for xTaskIncrementTick
    tasks.o(i.xTaskResumeAll) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskResumeAll) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskResumeAll) refers to tasks.o(.bss) for .bss
    timers.o(i.prvProcessExpiredTimer) refers to list.o(i.uxListRemove) for uxListRemove
    timers.o(i.prvProcessExpiredTimer) refers to list.o(i.vListInsert) for vListInsert
    timers.o(i.prvProcessExpiredTimer) refers to timers.o(.data) for .data
    timers.o(i.prvSampleTimeNow) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    timers.o(i.prvSampleTimeNow) refers to list.o(i.uxListRemove) for uxListRemove
    timers.o(i.prvSampleTimeNow) refers to list.o(i.vListInsert) for vListInsert
    timers.o(i.prvSampleTimeNow) refers to timers.o(.data) for .data
    timers.o(i.prvTimerTask) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    timers.o(i.prvTimerTask) refers to timers.o(i.prvSampleTimeNow) for prvSampleTimeNow
    timers.o(i.prvTimerTask) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    timers.o(i.prvTimerTask) refers to queue.o(i.xQueueReceive) for xQueueReceive
    timers.o(i.prvTimerTask) refers to list.o(i.uxListRemove) for uxListRemove
    timers.o(i.prvTimerTask) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    timers.o(i.prvTimerTask) refers to queue.o(i.vQueueWaitForMessageRestricted) for vQueueWaitForMessageRestricted
    timers.o(i.prvTimerTask) refers to timers.o(i.prvProcessExpiredTimer) for prvProcessExpiredTimer
    timers.o(i.prvTimerTask) refers to list.o(i.vListInsert) for vListInsert
    timers.o(i.prvTimerTask) refers to heap_4.o(i.vPortFree) for vPortFree
    timers.o(i.prvTimerTask) refers to timers.o(.data) for .data
    timers.o(i.pvTimerGetTimerID) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.pvTimerGetTimerID) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.uxTimerGetReloadMode) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.uxTimerGetReloadMode) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.vTimerResetState) refers to timers.o(.data) for .data
    timers.o(i.vTimerSetReloadMode) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.vTimerSetReloadMode) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.vTimerSetTimerID) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.vTimerSetTimerID) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.xTimerCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    timers.o(i.xTimerCreate) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.xTimerCreate) refers to list.o(i.vListInitialise) for vListInitialise
    timers.o(i.xTimerCreate) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    timers.o(i.xTimerCreate) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.xTimerCreate) refers to list.o(i.vListInitialiseItem) for vListInitialiseItem
    timers.o(i.xTimerCreate) refers to timers.o(.data) for .data
    timers.o(i.xTimerCreate) refers to timers.o(.bss) for .bss
    timers.o(i.xTimerCreateTimerTask) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.xTimerCreateTimerTask) refers to list.o(i.vListInitialise) for vListInitialise
    timers.o(i.xTimerCreateTimerTask) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    timers.o(i.xTimerCreateTimerTask) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.xTimerCreateTimerTask) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    timers.o(i.xTimerCreateTimerTask) refers to timers.o(.data) for .data
    timers.o(i.xTimerCreateTimerTask) refers to timers.o(.bss) for .bss
    timers.o(i.xTimerCreateTimerTask) refers to timers.o(i.prvTimerTask) for prvTimerTask
    timers.o(i.xTimerGenericCommandFromISR) refers to queue.o(i.xQueueGenericSendFromISR) for xQueueGenericSendFromISR
    timers.o(i.xTimerGenericCommandFromISR) refers to timers.o(.data) for .data
    timers.o(i.xTimerGenericCommandFromTask) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    timers.o(i.xTimerGenericCommandFromTask) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    timers.o(i.xTimerGenericCommandFromTask) refers to timers.o(.data) for .data
    timers.o(i.xTimerGetReloadMode) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.xTimerGetReloadMode) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.xTimerGetTimerDaemonTaskHandle) refers to timers.o(.data) for .data
    timers.o(i.xTimerIsTimerActive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.xTimerIsTimerActive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    atan2.o(i.__hardfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__hardfp_atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.__hardfp_atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.__hardfp_atan2) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2.o(i.__hardfp_atan2) refers to fabs.o(i.fabs) for fabs
    atan2.o(i.__hardfp_atan2) refers to dadd.o(.text) for __aeabi_dsub
    atan2.o(i.__hardfp_atan2) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    atan2.o(i.__hardfp_atan2) refers to errno.o(i.__set_errno) for __set_errno
    atan2.o(i.__hardfp_atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2.o(i.__softfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2.o(i.atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.____hardfp_atan2$lsc) refers to errno.o(i.__set_errno) for __set_errno
    atan2_x.o(i.____hardfp_atan2$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2_x.o(i.____hardfp_atan2$lsc) refers to fabs.o(i.fabs) for fabs
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dadd.o(.text) for __aeabi_dsub
    atan2_x.o(i.____hardfp_atan2$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    atan2_x.o(i.____hardfp_atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    round.o(i.__hardfp_round) refers (Special) to iusefp.o(.text) for __I$use$fp
    round.o(i.__hardfp_round) refers to drnd.o(.text) for _drnd
    round.o(i.__hardfp_round) refers to dadd.o(.text) for __aeabi_dsub
    round.o(i.__hardfp_round) refers to cdcmple.o(.text) for __aeabi_cdcmple
    round.o(i.__hardfp_round) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    round.o(i.round) refers (Special) to iusefp.o(.text) for __I$use$fp
    round.o(i.round) refers to drnd.o(.text) for _drnd
    round.o(i.round) refers to dadd.o(.text) for __aeabi_dsub
    round.o(i.round) refers to cdcmple.o(.text) for __aeabi_cdcmple
    round.o(i.round) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dneg.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    cdcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    atan.o(i.__hardfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.__hardfp_atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.__hardfp_atan) refers to fabs.o(i.fabs) for fabs
    atan.o(i.__hardfp_atan) refers to dadd.o(.text) for __aeabi_dadd
    atan.o(i.__hardfp_atan) refers to dmul.o(.text) for __aeabi_dmul
    atan.o(i.__hardfp_atan) refers to ddiv.o(.text) for __aeabi_ddiv
    atan.o(i.__hardfp_atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.__hardfp_atan) refers to atan.o(.constdata) for .constdata
    atan.o(i.__softfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(i.atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.____hardfp_atan$lsc) refers to fabs.o(i.fabs) for fabs
    atan_x.o(i.____hardfp_atan$lsc) refers to dadd.o(.text) for __aeabi_dadd
    atan_x.o(i.____hardfp_atan$lsc) refers to dmul.o(.text) for __aeabi_dmul
    atan_x.o(i.____hardfp_atan$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan_x.o(i.____hardfp_atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.____hardfp_atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(.text) for __aeabi_dmul
    fabs.o(i.__hardfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    drnd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    drnd.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    drnd.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f407xx.o(HEAP), (0 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing gpio.o(i.gpio_ex_callback_deregister), (20 bytes).
    Removing gpio.o(i.gpio_ex_callback_register), (72 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.rrx_text), (6 bytes).
    Removing sys.o(i.sys_intx_disable), (4 bytes).
    Removing sys.o(i.sys_intx_enable), (4 bytes).
    Removing sys.o(i.sys_msr_msp), (6 bytes).
    Removing sys.o(i.sys_nvic_set_vector_table), (16 bytes).
    Removing sys.o(i.sys_standby), (60 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing delay.o(.rrx_text), (6 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.fputc), (28 bytes).
    Removing usart.o(i.usart_iqr_callback_deregister), (16 bytes).
    Removing usart.o(i.usart_send_byte), (84 bytes).
    Removing usart.o(.data), (2 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing i2c.o(i.i2c_ack), (72 bytes).
    Removing i2c.o(i.i2c_delay), (6 bytes).
    Removing i2c.o(i.i2c_nack), (64 bytes).
    Removing i2c.o(i.i2c_read_byte), (196 bytes).
    Removing i2c.o(i.i2c_start), (68 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing dma.o(i.DMA2_Channel3_IRQHandler), (2 bytes).
    Removing dma.o(.data), (4 bytes).
    Removing spi.o(.rev16_text), (4 bytes).
    Removing spi.o(.revsh_text), (4 bytes).
    Removing spi.o(.rrx_text), (6 bytes).
    Removing spi.o(i.spi_pin_init), (304 bytes).
    Removing spi.o(i.spi_read_write_byte), (60 bytes).
    Removing spi.o(i.spi_set_speed), (72 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.rrx_text), (6 bytes).
    Removing adc.o(i.adc_get_value), (56 bytes).
    Removing adc.o(.data), (1 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.btimx_int_init), (60 bytes).
    Removing tim.o(i.tim_cap_get_value), (44 bytes).
    Removing tim.o(i.tim_cap_pin_init), (576 bytes).
    Removing tim.o(i.tim_cnt_get_value), (168 bytes).
    Removing wdg.o(.rev16_text), (4 bytes).
    Removing wdg.o(.revsh_text), (4 bytes).
    Removing wdg.o(.rrx_text), (6 bytes).
    Removing wdg.o(i.iwdg_feed), (16 bytes).
    Removing wdg.o(i.iwdg_init), (28 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing led.o(.rrx_text), (6 bytes).
    Removing led.o(i.led_set), (40 bytes).
    Removing debug.o(.rev16_text), (4 bytes).
    Removing debug.o(.revsh_text), (4 bytes).
    Removing debug.o(.rrx_text), (6 bytes).
    Removing debug.o(i.debug_message), (120 bytes).
    Removing debug.o(i.debug_send_data), (10 bytes).
    Removing debug.o(i.debug_warning), (124 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(.rrx_text), (6 bytes).
    Removing oled.o(i.OLED_Clear), (32 bytes).
    Removing oled.o(i.OLED_ClearArea), (120 bytes).
    Removing oled.o(i.OLED_DrawArc), (632 bytes).
    Removing oled.o(i.OLED_DrawCircle), (364 bytes).
    Removing oled.o(i.OLED_DrawEllipse), (800 bytes).
    Removing oled.o(i.OLED_DrawLine), (296 bytes).
    Removing oled.o(i.OLED_DrawPoint), (60 bytes).
    Removing oled.o(i.OLED_DrawRectangle), (140 bytes).
    Removing oled.o(i.OLED_DrawTriangle), (330 bytes).
    Removing oled.o(i.OLED_GPIO_Init), (30 bytes).
    Removing oled.o(i.OLED_GetPoint), (60 bytes).
    Removing oled.o(i.OLED_IsInAngle), (136 bytes).
    Removing oled.o(i.OLED_Pow), (34 bytes).
    Removing oled.o(i.OLED_Reverse), (52 bytes).
    Removing oled.o(i.OLED_ReverseArea), (120 bytes).
    Removing oled.o(i.OLED_SetCursor), (80 bytes).
    Removing oled.o(i.OLED_ShowBinNum), (104 bytes).
    Removing oled.o(i.OLED_ShowFloatNum), (436 bytes).
    Removing oled.o(i.OLED_ShowHexNum), (128 bytes).
    Removing oled.o(i.OLED_ShowNum), (120 bytes).
    Removing oled.o(i.OLED_ShowSignedNum), (144 bytes).
    Removing oled.o(i.OLED_UpdateArea), (180 bytes).
    Removing oled.o(i.OLED_WriteCommand), (22 bytes).
    Removing oled.o(i.OLED_WriteData), (18 bytes).
    Removing oled.o(i.OLED_pnpoly), (122 bytes).
    Removing oled_data.o(.constdata), (32 bytes).
    Removing adc_key.o(.rev16_text), (4 bytes).
    Removing adc_key.o(.revsh_text), (4 bytes).
    Removing adc_key.o(.rrx_text), (6 bytes).
    Removing gpio_key.o(.rev16_text), (4 bytes).
    Removing gpio_key.o(.revsh_text), (4 bytes).
    Removing gpio_key.o(.rrx_text), (6 bytes).
    Removing gpio_key.o(i.gpio_key_read), (48 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing key.o(.rrx_text), (6 bytes).
    Removing tb6612.o(.rev16_text), (4 bytes).
    Removing tb6612.o(.revsh_text), (4 bytes).
    Removing tb6612.o(.rrx_text), (6 bytes).
    Removing tb6612.o(i.tb6612_init), (116 bytes).
    Removing tb6612.o(i.tb6612_set_direction), (64 bytes).
    Removing tb6612.o(.constdata), (10 bytes).
    Removing encoder.o(.rev16_text), (4 bytes).
    Removing encoder.o(.revsh_text), (4 bytes).
    Removing encoder.o(.rrx_text), (6 bytes).
    Removing encoder.o(i.encoder_get_value), (32 bytes).
    Removing track.o(.rev16_text), (4 bytes).
    Removing track.o(.revsh_text), (4 bytes).
    Removing track.o(.rrx_text), (6 bytes).
    Removing track.o(i.rrrr), (76 bytes).
    Removing track.o(.constdata), (32 bytes).
    Removing track.o(.data), (4 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(i.board_init), (18 bytes).
    Removing main.o(i.sys_init), (84 bytes).
    Removing app_key.o(.rev16_text), (4 bytes).
    Removing app_key.o(.revsh_text), (4 bytes).
    Removing app_key.o(.rrx_text), (6 bytes).
    Removing app_key.o(i.app_key_task_stop), (12 bytes).
    Removing app_state.o(.rev16_text), (4 bytes).
    Removing app_state.o(.revsh_text), (4 bytes).
    Removing app_state.o(.rrx_text), (6 bytes).
    Removing app_state.o(i.app_state_task_stop), (12 bytes).
    Removing app_ui.o(.rev16_text), (4 bytes).
    Removing app_ui.o(.revsh_text), (4 bytes).
    Removing app_ui.o(.rrx_text), (6 bytes).
    Removing app_ui.o(i.app_ui_task_stop), (12 bytes).
    Removing malloc.o(.rev16_text), (4 bytes).
    Removing malloc.o(.revsh_text), (4 bytes).
    Removing malloc.o(.rrx_text), (6 bytes).
    Removing malloc.o(i.my_mem_copy), (48 bytes).
    Removing malloc.o(i.my_mem_malloc), (196 bytes).
    Removing malloc.o(i.my_mem_set), (36 bytes).
    Removing malloc.o(i.myfree), (128 bytes).
    Removing malloc.o(i.mymalloc), (36 bytes).
    Removing malloc.o(i.myrealloc), (196 bytes).
    Removing usmart.o(.rev16_text), (4 bytes).
    Removing usmart.o(.revsh_text), (4 bytes).
    Removing usmart.o(.rrx_text), (6 bytes).
    Removing usmart_config.o(.rev16_text), (4 bytes).
    Removing usmart_config.o(.revsh_text), (4 bytes).
    Removing usmart_config.o(.rrx_text), (6 bytes).
    Removing usmart_port.o(.rev16_text), (4 bytes).
    Removing usmart_port.o(.revsh_text), (4 bytes).
    Removing usmart_port.o(.rrx_text), (6 bytes).
    Removing usmart_str.o(.rev16_text), (4 bytes).
    Removing usmart_str.o(.revsh_text), (4 bytes).
    Removing usmart_str.o(.rrx_text), (6 bytes).
    Removing usmart_str.o(i.usmart_pow), (40 bytes).
    Removing usmart_str.o(i.usmart_search_nextc), (20 bytes).
    Removing usmart_str.o(i.usmart_strcopy), (18 bytes).
    Removing usmart_str.o(i.usmart_strlen), (20 bytes).
    Removing event_groups.o(i.uxEventGroupGetNumber), (8 bytes).
    Removing event_groups.o(i.vEventGroupClearBitsCallback), (24 bytes).
    Removing event_groups.o(i.vEventGroupDelete), (46 bytes).
    Removing event_groups.o(i.vEventGroupSetBitsCallback), (106 bytes).
    Removing event_groups.o(i.vEventGroupSetNumber), (4 bytes).
    Removing event_groups.o(i.xEventGroupClearBits), (26 bytes).
    Removing event_groups.o(i.xEventGroupCreate), (26 bytes).
    Removing event_groups.o(i.xEventGroupGetBitsFromISR), (26 bytes).
    Removing event_groups.o(i.xEventGroupSetBits), (108 bytes).
    Removing event_groups.o(i.xEventGroupSync), (236 bytes).
    Removing event_groups.o(i.xEventGroupWaitBits), (188 bytes).
    Removing heap_4.o(i.pvPortCalloc), (38 bytes).
    Removing heap_4.o(i.vPortGetHeapStats), (120 bytes).
    Removing heap_4.o(i.vPortHeapResetState), (20 bytes).
    Removing heap_4.o(i.vPortInitialiseBlocks), (2 bytes).
    Removing heap_4.o(i.xPortGetMinimumEverFreeHeapSize), (12 bytes).
    Removing port.o(i.vPortEndScheduler), (2 bytes).
    Removing queue.o(i.ucQueueGetQueueType), (6 bytes).
    Removing queue.o(i.uxQueueGetQueueItemSize), (4 bytes).
    Removing queue.o(i.uxQueueGetQueueLength), (4 bytes).
    Removing queue.o(i.uxQueueGetQueueNumber), (4 bytes).
    Removing queue.o(i.uxQueueMessagesWaiting), (18 bytes).
    Removing queue.o(i.uxQueueMessagesWaitingFromISR), (4 bytes).
    Removing queue.o(i.uxQueueSpacesAvailable), (22 bytes).
    Removing queue.o(i.vQueueDelete), (4 bytes).
    Removing queue.o(i.vQueueSetQueueNumber), (4 bytes).
    Removing queue.o(i.xQueueCreateMutex), (148 bytes).
    Removing queue.o(i.xQueueGenericReset), (148 bytes).
    Removing queue.o(i.xQueueGenericSend), (368 bytes).
    Removing queue.o(i.xQueueGenericSendFromISR), (214 bytes).
    Removing queue.o(i.xQueueGiveFromISR), (102 bytes).
    Removing queue.o(i.xQueueIsQueueEmptyFromISR), (12 bytes).
    Removing queue.o(i.xQueueIsQueueFullFromISR), (14 bytes).
    Removing queue.o(i.xQueuePeek), (276 bytes).
    Removing queue.o(i.xQueuePeekFromISR), (64 bytes).
    Removing queue.o(i.xQueueReceiveFromISR), (134 bytes).
    Removing queue.o(i.xQueueSemaphoreTake), (328 bytes).
    Removing tasks.o(i.eTaskGetState), (88 bytes).
    Removing tasks.o(i.pcTaskGetName), (16 bytes).
    Removing tasks.o(i.pvTaskIncrementMutexHeldCount), (24 bytes).
    Removing tasks.o(i.ulTaskGenericNotifyTake), (156 bytes).
    Removing tasks.o(i.ulTaskGenericNotifyValueClear), (44 bytes).
    Removing tasks.o(i.uxTaskGetNumberOfTasks), (12 bytes).
    Removing tasks.o(i.uxTaskGetSystemState), (160 bytes).
    Removing tasks.o(i.uxTaskGetTaskNumber), (8 bytes).
    Removing tasks.o(i.uxTaskResetEventItemValue), (24 bytes).
    Removing tasks.o(i.vTaskEndScheduler), (300 bytes).
    Removing tasks.o(i.vTaskGenericNotifyGiveFromISR), (284 bytes).
    Removing tasks.o(i.vTaskGetInfo), (208 bytes).
    Removing tasks.o(i.vTaskPlaceOnUnorderedEventList), (88 bytes).
    Removing tasks.o(i.vTaskPriorityDisinheritAfterTimeout), (168 bytes).
    Removing tasks.o(i.vTaskRemoveFromUnorderedEventList), (188 bytes).
    Removing tasks.o(i.vTaskResetState), (40 bytes).
    Removing tasks.o(i.vTaskSetTaskNumber), (8 bytes).
    Removing tasks.o(i.vTaskSetTimeOutState), (32 bytes).
    Removing tasks.o(i.xTaskCatchUpTicks), (40 bytes).
    Removing tasks.o(i.xTaskGenericNotify), (256 bytes).
    Removing tasks.o(i.xTaskGenericNotifyFromISR), (316 bytes).
    Removing tasks.o(i.xTaskGenericNotifyStateClear), (52 bytes).
    Removing tasks.o(i.xTaskGenericNotifyWait), (192 bytes).
    Removing tasks.o(i.xTaskGetCurrentTaskHandle), (12 bytes).
    Removing tasks.o(i.xTaskGetCurrentTaskHandleForCore), (20 bytes).
    Removing tasks.o(i.xTaskGetTickCountFromISR), (12 bytes).
    Removing tasks.o(i.xTaskPriorityDisinherit), (144 bytes).
    Removing tasks.o(i.xTaskPriorityInherit), (184 bytes).
    Removing timers.o(i.pcTimerGetName), (4 bytes).
    Removing timers.o(i.pvTimerGetTimerID), (18 bytes).
    Removing timers.o(i.uxTimerGetReloadMode), (30 bytes).
    Removing timers.o(i.uxTimerGetTimerNumber), (4 bytes).
    Removing timers.o(i.vTimerResetState), (16 bytes).
    Removing timers.o(i.vTimerSetReloadMode), (38 bytes).
    Removing timers.o(i.vTimerSetTimerID), (20 bytes).
    Removing timers.o(i.vTimerSetTimerNumber), (4 bytes).
    Removing timers.o(i.xTimerCreate), (128 bytes).
    Removing timers.o(i.xTimerGenericCommandFromISR), (64 bytes).
    Removing timers.o(i.xTimerGenericCommandFromTask), (84 bytes).
    Removing timers.o(i.xTimerGetExpiryTime), (4 bytes).
    Removing timers.o(i.xTimerGetPeriod), (4 bytes).
    Removing timers.o(i.xTimerGetReloadMode), (30 bytes).
    Removing timers.o(i.xTimerGetTimerDaemonTaskHandle), (12 bytes).
    Removing timers.o(i.xTimerIsTimerActive), (30 bytes).
    Removing dneg.o(.text), (6 bytes).
    Removing dflti.o(.text), (34 bytes).
    Removing dfltui.o(.text), (26 bytes).
    Removing dfixi.o(.text), (62 bytes).
    Removing dfixui.o(.text), (50 bytes).
    Removing cdcmple.o(.text), (48 bytes).
    Removing d2f.o(.text), (56 bytes).
    Removing fepilogue.o(.text), (110 bytes).
    Removing drnd.o(.text), (136 bytes).

257 unused section(s) (total 15609 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/../cmprslib/zerorunl2.c          0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strcmp.c         0x00000000   Number         0  strcmp.o ABSOLUTE
    ../clib/microlib/string/strcpy.c         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixui.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpneg.c                0x00000000   Number         0  dneg.o ABSOLUTE
    ../fplib/microlib/fprnd.c                0x00000000   Number         0  drnd.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/round.c                       0x00000000   Number         0  round.o ABSOLUTE
    ..\App\app_key.c                         0x00000000   Number         0  app_key.o ABSOLUTE
    ..\App\app_state.c                       0x00000000   Number         0  app_state.o ABSOLUTE
    ..\App\app_ui.c                          0x00000000   Number         0  app_ui.o ABSOLUTE
    ..\App\main.c                            0x00000000   Number         0  main.o ABSOLUTE
    ..\Drivers\BSP\DEBUG\debug.c             0x00000000   Number         0  debug.o ABSOLUTE
    ..\Drivers\BSP\ENCODER\encoder.c         0x00000000   Number         0  encoder.o ABSOLUTE
    ..\Drivers\BSP\KEY\adc_key.c             0x00000000   Number         0  adc_key.o ABSOLUTE
    ..\Drivers\BSP\KEY\gpio_key.c            0x00000000   Number         0  gpio_key.o ABSOLUTE
    ..\Drivers\BSP\KEY\key.c                 0x00000000   Number         0  key.o ABSOLUTE
    ..\Drivers\BSP\LED\led.c                 0x00000000   Number         0  led.o ABSOLUTE
    ..\Drivers\BSP\OLED\OLED.c               0x00000000   Number         0  oled.o ABSOLUTE
    ..\Drivers\BSP\OLED\OLED_Data.c          0x00000000   Number         0  oled_data.o ABSOLUTE
    ..\Drivers\BSP\TB6612\tb6612.c           0x00000000   Number         0  tb6612.o ABSOLUTE
    ..\Drivers\BSP\TRACK\track.c             0x00000000   Number         0  track.o ABSOLUTE
    ..\Drivers\CMSIS\Device\ST\STM32F4xx\Source\Templates\arm\startup_stm32f407xx.s 0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    ..\Drivers\PORT\adc.c                    0x00000000   Number         0  adc.o ABSOLUTE
    ..\Drivers\PORT\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\Drivers\PORT\dma.c                    0x00000000   Number         0  dma.o ABSOLUTE
    ..\Drivers\PORT\gpio.c                   0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Drivers\PORT\i2c.c                    0x00000000   Number         0  i2c.o ABSOLUTE
    ..\Drivers\PORT\spi.c                    0x00000000   Number         0  spi.o ABSOLUTE
    ..\Drivers\PORT\sys.c                    0x00000000   Number         0  sys.o ABSOLUTE
    ..\Drivers\PORT\tim.c                    0x00000000   Number         0  tim.o ABSOLUTE
    ..\Drivers\PORT\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\PORT\wdg.c                    0x00000000   Number         0  wdg.o ABSOLUTE
    ..\Middlewares\MALLOC\malloc.c           0x00000000   Number         0  malloc.o ABSOLUTE
    ..\Middlewares\USMART\usmart.c           0x00000000   Number         0  usmart.o ABSOLUTE
    ..\Middlewares\USMART\usmart_config.c    0x00000000   Number         0  usmart_config.o ABSOLUTE
    ..\Middlewares\USMART\usmart_port.c      0x00000000   Number         0  usmart_port.o ABSOLUTE
    ..\Middlewares\USMART\usmart_str.c       0x00000000   Number         0  usmart_str.o ABSOLUTE
    ..\Middlewares\freertos\src\croutine.c   0x00000000   Number         0  croutine.o ABSOLUTE
    ..\Middlewares\freertos\src\event_groups.c 0x00000000   Number         0  event_groups.o ABSOLUTE
    ..\Middlewares\freertos\src\heap_4.c     0x00000000   Number         0  heap_4.o ABSOLUTE
    ..\Middlewares\freertos\src\list.c       0x00000000   Number         0  list.o ABSOLUTE
    ..\Middlewares\freertos\src\port.c       0x00000000   Number         0  port.o ABSOLUTE
    ..\Middlewares\freertos\src\queue.c      0x00000000   Number         0  queue.o ABSOLUTE
    ..\Middlewares\freertos\src\stream_buffer.c 0x00000000   Number         0  stream_buffer.o ABSOLUTE
    ..\Middlewares\freertos\src\tasks.c      0x00000000   Number         0  tasks.o ABSOLUTE
    ..\Middlewares\freertos\src\timers.c     0x00000000   Number         0  timers.o ABSOLUTE
    ..\\App\\app_key.c                       0x00000000   Number         0  app_key.o ABSOLUTE
    ..\\App\\app_state.c                     0x00000000   Number         0  app_state.o ABSOLUTE
    ..\\App\\app_ui.c                        0x00000000   Number         0  app_ui.o ABSOLUTE
    ..\\App\\main.c                          0x00000000   Number         0  main.o ABSOLUTE
    ..\\Drivers\\BSP\\DEBUG\\debug.c         0x00000000   Number         0  debug.o ABSOLUTE
    ..\\Drivers\\BSP\\ENCODER\\encoder.c     0x00000000   Number         0  encoder.o ABSOLUTE
    ..\\Drivers\\BSP\\KEY\\adc_key.c         0x00000000   Number         0  adc_key.o ABSOLUTE
    ..\\Drivers\\BSP\\KEY\\gpio_key.c        0x00000000   Number         0  gpio_key.o ABSOLUTE
    ..\\Drivers\\BSP\\KEY\\key.c             0x00000000   Number         0  key.o ABSOLUTE
    ..\\Drivers\\BSP\\LED\\led.c             0x00000000   Number         0  led.o ABSOLUTE
    ..\\Drivers\\BSP\\OLED\\OLED.c           0x00000000   Number         0  oled.o ABSOLUTE
    ..\\Drivers\\BSP\\TB6612\\tb6612.c       0x00000000   Number         0  tb6612.o ABSOLUTE
    ..\\Drivers\\BSP\\TRACK\\track.c         0x00000000   Number         0  track.o ABSOLUTE
    ..\\Drivers\\PORT\\adc.c                 0x00000000   Number         0  adc.o ABSOLUTE
    ..\\Drivers\\PORT\\delay.c               0x00000000   Number         0  delay.o ABSOLUTE
    ..\\Drivers\\PORT\\dma.c                 0x00000000   Number         0  dma.o ABSOLUTE
    ..\\Drivers\\PORT\\gpio.c                0x00000000   Number         0  gpio.o ABSOLUTE
    ..\\Drivers\\PORT\\i2c.c                 0x00000000   Number         0  i2c.o ABSOLUTE
    ..\\Drivers\\PORT\\spi.c                 0x00000000   Number         0  spi.o ABSOLUTE
    ..\\Drivers\\PORT\\sys.c                 0x00000000   Number         0  sys.o ABSOLUTE
    ..\\Drivers\\PORT\\tim.c                 0x00000000   Number         0  tim.o ABSOLUTE
    ..\\Drivers\\PORT\\usart.c               0x00000000   Number         0  usart.o ABSOLUTE
    ..\\Drivers\\PORT\\wdg.c                 0x00000000   Number         0  wdg.o ABSOLUTE
    ..\\Middlewares\\MALLOC\\malloc.c        0x00000000   Number         0  malloc.o ABSOLUTE
    ..\\Middlewares\\USMART\\usmart.c        0x00000000   Number         0  usmart.o ABSOLUTE
    ..\\Middlewares\\USMART\\usmart_config.c 0x00000000   Number         0  usmart_config.o ABSOLUTE
    ..\\Middlewares\\USMART\\usmart_port.c   0x00000000   Number         0  usmart_port.o ABSOLUTE
    ..\\Middlewares\\USMART\\usmart_str.c    0x00000000   Number         0  usmart_str.o ABSOLUTE
    ..\\Middlewares\\freertos\\src\\port.c   0x00000000   Number         0  port.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000188   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000188   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x0800018c   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000190   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000190   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000190   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x08000198   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x0800019c   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x0800019c   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x0800019c   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x0800019c   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .emb_text                                0x080001a0   Section      190  port.o(.emb_text)
    $v0                                      0x080001a0   Number         0  port.o(.emb_text)
    .text                                    0x08000260   Section       44  startup_stm32f407xx.o(.text)
    $v0                                      0x08000260   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x0800028c   Section        0  memcpya.o(.text)
    .text                                    0x080002b0   Section        0  memseta.o(.text)
    .text                                    0x080002d4   Section        0  strlen.o(.text)
    .text                                    0x080002e2   Section        0  strcmp.o(.text)
    .text                                    0x080002fe   Section        0  strcpy.o(.text)
    .text                                    0x08000310   Section        0  dadd.o(.text)
    .text                                    0x0800045e   Section        0  dmul.o(.text)
    .text                                    0x08000542   Section        0  ddiv.o(.text)
    .text                                    0x08000620   Section       48  cdrcmple.o(.text)
    .text                                    0x08000650   Section        0  uidiv.o(.text)
    .text                                    0x0800067c   Section        0  uldiv.o(.text)
    .text                                    0x080006de   Section        0  llshl.o(.text)
    .text                                    0x080006fc   Section        0  llushr.o(.text)
    .text                                    0x0800071c   Section        0  llsshr.o(.text)
    .text                                    0x08000740   Section        0  iusefp.o(.text)
    .text                                    0x08000740   Section        0  depilogue.o(.text)
    .text                                    0x080007fa   Section        0  dfixul.o(.text)
    .text                                    0x0800082c   Section       36  init.o(.text)
    .text                                    0x08000850   Section        0  __dczerorl2.o(.text)
    i.EXTI0_IRQHandler                       0x080008a8   Section        0  gpio.o(i.EXTI0_IRQHandler)
    i.EXTI15_10_IRQHandler                   0x080008d0   Section        0  gpio.o(i.EXTI15_10_IRQHandler)
    i.EXTI1_IRQHandler                       0x08000978   Section        0  gpio.o(i.EXTI1_IRQHandler)
    i.EXTI2_IRQHandler                       0x080009a0   Section        0  gpio.o(i.EXTI2_IRQHandler)
    i.EXTI3_IRQHandler                       0x080009c8   Section        0  gpio.o(i.EXTI3_IRQHandler)
    i.EXTI4_IRQHandler                       0x080009f0   Section        0  gpio.o(i.EXTI4_IRQHandler)
    i.EXTI9_5_IRQHandler                     0x08000a18   Section        0  gpio.o(i.EXTI9_5_IRQHandler)
    i.HardFault_Handler                      0x08000aa8   Section        0  sys.o(i.HardFault_Handler)
    i.OLED_Init                              0x08000b14   Section        0  oled.o(i.OLED_Init)
    i.OLED_Printf                            0x08000d84   Section        0  oled.o(i.OLED_Printf)
    i.OLED_ShowChar                          0x08000db0   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowImage                         0x08000df0   Section        0  oled.o(i.OLED_ShowImage)
    i.OLED_ShowString                        0x08000f28   Section        0  oled.o(i.OLED_ShowString)
    i.OLED_Update                            0x0800102c   Section        0  oled.o(i.OLED_Update)
    i.PendSV_Handler                         0x080010a0   Section        0  sys.o(i.PendSV_Handler)
    i.SVC_Handler                            0x080010a4   Section        0  sys.o(i.SVC_Handler)
    i.SysTick_Handler                        0x080010a8   Section        0  sys.o(i.SysTick_Handler)
    i.TIM1_BRK_TIM9_IRQHandler               0x080010d0   Section        0  tim.o(i.TIM1_BRK_TIM9_IRQHandler)
    i.TIM1_CC_IRQHandler                     0x080010e4   Section        0  tim.o(i.TIM1_CC_IRQHandler)
    i.TIM1_TRG_COM_TIM11_IRQHandler          0x080010f8   Section        0  tim.o(i.TIM1_TRG_COM_TIM11_IRQHandler)
    i.TIM1_UP_TIM10_IRQHandler               0x0800110c   Section        0  tim.o(i.TIM1_UP_TIM10_IRQHandler)
    i.TIM2_IRQHandler                        0x08001120   Section        0  tim.o(i.TIM2_IRQHandler)
    i.TIM3_IRQHandler                        0x08001134   Section        0  tim.o(i.TIM3_IRQHandler)
    i.TIM4_IRQHandler                        0x08001148   Section        0  usmart_port.o(i.TIM4_IRQHandler)
    i.TIM5_IRQHandler                        0x08001184   Section        0  tim.o(i.TIM5_IRQHandler)
    i.TIM6_DAC_IRQHandler                    0x08001198   Section        0  tim.o(i.TIM6_DAC_IRQHandler)
    i.TIM7_IRQHandler                        0x080011f8   Section        0  tim.o(i.TIM7_IRQHandler)
    i.TIM8_BRK_TIM12_IRQHandler              0x0800120c   Section        0  tim.o(i.TIM8_BRK_TIM12_IRQHandler)
    i.TIM8_CC_IRQHandler                     0x08001220   Section        0  tim.o(i.TIM8_CC_IRQHandler)
    i.TIM8_TRG_COM_TIM14_IRQHandler          0x08001234   Section        0  tim.o(i.TIM8_TRG_COM_TIM14_IRQHandler)
    i.TIM8_UP_TIM13_IRQHandler               0x08001248   Section        0  tim.o(i.TIM8_UP_TIM13_IRQHandler)
    i.UART4_IRQHandler                       0x0800125c   Section        0  usart.o(i.UART4_IRQHandler)
    i.UART5_IRQHandler                       0x08001288   Section        0  usart.o(i.UART5_IRQHandler)
    i.USART1_IRQHandler                      0x080012ac   Section        0  usart.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x080012d0   Section        0  usart.o(i.USART2_IRQHandler)
    i.USART3_IRQHandler                      0x080012fc   Section        0  usart.o(i.USART3_IRQHandler)
    i.USART6_IRQHandler                      0x08001328   Section        0  usart.o(i.USART6_IRQHandler)
    i.__0snprintf                            0x08001354   Section        0  printfa.o(i.__0snprintf)
    i.__0vsnprintf                           0x08001388   Section        0  printfa.o(i.__0vsnprintf)
    i.__0vsprintf                            0x080013bc   Section        0  printfa.o(i.__0vsprintf)
    i.__ARM_common_memclr4_10                0x080013e0   Section        0  usmart_str.o(i.__ARM_common_memclr4_10)
    i.__scatterload_copy                     0x080013ea   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x080013f8   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x080013fa   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._fp_digits                             0x08001408   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x08001409   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x0800158c   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x0800158d   Thumb Code  1704  printfa.o(i._printf_core)
    i._printf_post_padding                   0x08001c40   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x08001c41   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08001c64   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08001c65   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._snputc                                0x08001c92   Section        0  printfa.o(i._snputc)
    _snputc                                  0x08001c93   Thumb Code    22  printfa.o(i._snputc)
    i._sputc                                 0x08001ca8   Section        0  printfa.o(i._sputc)
    _sputc                                   0x08001ca9   Thumb Code    10  printfa.o(i._sputc)
    i.adc_pin_init                           0x08001cb4   Section        0  adc.o(i.adc_pin_init)
    i.app_init                               0x08001f38   Section        0  main.o(i.app_init)
    i.app_key_task                           0x0800206c   Section        0  app_key.o(i.app_key_task)
    app_key_task                             0x0800206d   Thumb Code    84  app_key.o(i.app_key_task)
    i.app_key_task_start                     0x080020dc   Section        0  app_key.o(i.app_key_task_start)
    i.app_state_task                         0x08002128   Section        0  app_state.o(i.app_state_task)
    app_state_task                           0x08002129   Thumb Code   144  app_state.o(i.app_state_task)
    i.app_state_task_start                   0x08002274   Section        0  app_state.o(i.app_state_task_start)
    i.app_ui_task                            0x080022bc   Section        0  app_ui.o(i.app_ui_task)
    app_ui_task                              0x080022bd   Thumb Code    82  app_ui.o(i.app_ui_task)
    i.app_ui_task_start                      0x0800232c   Section        0  app_ui.o(i.app_ui_task_start)
    i.debug_error                            0x08002370   Section        0  debug.o(i.debug_error)
    i.debug_init                             0x080023e8   Section        0  debug.o(i.debug_init)
    i.debug_printf                           0x080024c8   Section        0  debug.o(i.debug_printf)
    i.delay_init                             0x08002528   Section        0  delay.o(i.delay_init)
    i.delay_ms                               0x0800254c   Section        0  delay.o(i.delay_ms)
    i.delay_us                               0x08002584   Section        0  delay.o(i.delay_us)
    i.dma_basic_config                       0x080025b8   Section        0  dma.o(i.dma_basic_config)
    i.dma_enable                             0x08002600   Section        0  dma.o(i.dma_enable)
    i.encoder_init                           0x0800261c   Section        0  encoder.o(i.encoder_init)
    i.get_debug_time                         0x08002658   Section        0  debug.o(i.get_debug_time)
    i.gpio_af_set                            0x080026c8   Section        0  gpio.o(i.gpio_af_set)
    i.gpio_key_get_state                     0x08002734   Section        0  gpio_key.o(i.gpio_key_get_state)
    i.gpio_key_init                          0x0800274c   Section        0  gpio_key.o(i.gpio_key_init)
    i.gpio_key_scan                          0x08002794   Section        0  gpio_key.o(i.gpio_key_scan)
    i.gpio_nvic_ex_config                    0x080028bc   Section        0  gpio.o(i.gpio_nvic_ex_config)
    i.gpio_pin_init                          0x080029e4   Section        0  gpio.o(i.gpio_pin_init)
    i.gpio_read_pin                          0x08002b6c   Section        0  gpio.o(i.gpio_read_pin)
    i.gpio_toggle_pin                        0x08002b88   Section        0  gpio.o(i.gpio_toggle_pin)
    i.gpio_write_pin                         0x08002ba0   Section        0  gpio.o(i.gpio_write_pin)
    i.i2c_init                               0x08002bc4   Section        0  i2c.o(i.i2c_init)
    i.i2c_read_data                          0x08002bf4   Section        0  i2c.o(i.i2c_read_data)
    i.i2c_send_byte                          0x08002e68   Section        0  i2c.o(i.i2c_send_byte)
    i.i2c_stop                               0x08002ec0   Section        0  i2c.o(i.i2c_stop)
    i.i2c_wait_ack                           0x08002efc   Section        0  i2c.o(i.i2c_wait_ack)
    i.i2c_write_data                         0x08002f88   Section        0  i2c.o(i.i2c_write_data)
    i.key_get_value                          0x0800309c   Section        0  key.o(i.key_get_value)
    i.key_init                               0x080030a0   Section        0  key.o(i.key_init)
    i.key_scan                               0x080030b8   Section        0  key.o(i.key_scan)
    i.led_init                               0x080030bc   Section        0  led.o(i.led_init)
    i.led_test                               0x080030f4   Section        0  main.o(i.led_test)
    i.led_toggle                             0x080030fc   Section        0  led.o(i.led_toggle)
    i.main                                   0x0800310c   Section        0  main.o(i.main)
    i.my_mem_init                            0x08003240   Section        0  malloc.o(i.my_mem_init)
    i.my_mem_perused                         0x08003278   Section        0  malloc.o(i.my_mem_perused)
    i.my_printf                              0x080032b4   Section        0  debug.o(i.my_printf)
    i.prvAddCurrentTaskToDelayedList         0x08003300   Section        0  tasks.o(i.prvAddCurrentTaskToDelayedList)
    prvAddCurrentTaskToDelayedList           0x08003301   Thumb Code    90  tasks.o(i.prvAddCurrentTaskToDelayedList)
    i.prvIdleTask                            0x08003360   Section        0  tasks.o(i.prvIdleTask)
    prvIdleTask                              0x08003361   Thumb Code    88  tasks.o(i.prvIdleTask)
    i.prvListTasksWithinSingleList           0x080033c4   Section        0  tasks.o(i.prvListTasksWithinSingleList)
    prvListTasksWithinSingleList             0x080033c5   Thumb Code   242  tasks.o(i.prvListTasksWithinSingleList)
    i.prvProcessExpiredTimer                 0x080034c4   Section        0  timers.o(i.prvProcessExpiredTimer)
    prvProcessExpiredTimer                   0x080034c5   Thumb Code   156  timers.o(i.prvProcessExpiredTimer)
    i.prvSampleTimeNow                       0x08003564   Section        0  timers.o(i.prvSampleTimeNow)
    prvSampleTimeNow                         0x08003565   Thumb Code   208  timers.o(i.prvSampleTimeNow)
    i.prvTaskExitError                       0x08003638   Section        0  port.o(i.prvTaskExitError)
    prvTaskExitError                         0x08003639   Thumb Code    16  port.o(i.prvTaskExitError)
    i.prvTimerTask                           0x08003648   Section        0  timers.o(i.prvTimerTask)
    prvTimerTask                             0x08003649   Thumb Code   702  timers.o(i.prvTimerTask)
    i.prvUnlockQueue                         0x08003910   Section        0  queue.o(i.prvUnlockQueue)
    prvUnlockQueue                           0x08003911   Thumb Code   108  queue.o(i.prvUnlockQueue)
    i.pvPortMalloc                           0x0800397c   Section        0  heap_4.o(i.pvPortMalloc)
    i.pxPortInitialiseStack                  0x08003a74   Section        0  port.o(i.pxPortInitialiseStack)
    i.read_addr                              0x08003aa0   Section        0  usmart.o(i.read_addr)
    i.sys_check_rst                          0x08003aa4   Section        0  sys.o(i.sys_check_rst)
    i.sys_clock_set                          0x08003bd0   Section        0  sys.o(i.sys_clock_set)
    i.sys_get_tick                           0x08003cf8   Section        0  sys.o(i.sys_get_tick)
    i.sys_nvic_init                          0x08003d04   Section        0  sys.o(i.sys_nvic_init)
    i.sys_soft_reset                         0x08003d74   Section        0  sys.o(i.sys_soft_reset)
    i.sys_stm32_clock_init                   0x08003d84   Section        0  sys.o(i.sys_stm32_clock_init)
    i.task_test                              0x08003dcc   Section        0  main.o(i.task_test)
    i.tim_cap_irq_callback                   0x08003e20   Section        0  tim.o(i.tim_cap_irq_callback)
    tim_cap_irq_callback                     0x08003e21   Thumb Code   292  tim.o(i.tim_cap_irq_callback)
    i.tim_cnt_pin_init                       0x08003f7c   Section        0  tim.o(i.tim_cnt_pin_init)
    i.tim_cnt_set_value                      0x080041f0   Section        0  tim.o(i.tim_cnt_set_value)
    i.tim_int_callback_deregister            0x08004298   Section        0  tim.o(i.tim_int_callback_deregister)
    i.tim_int_callback_register              0x08004314   Section        0  tim.o(i.tim_int_callback_register)
    i.tim_pwm_pin_init                       0x080043cc   Section        0  tim.o(i.tim_pwm_pin_init)
    i.tim_pwm_set_ccr                        0x080045f0   Section        0  tim.o(i.tim_pwm_set_ccr)
    i.track_get_sensor_data                  0x080046b4   Section        0  track.o(i.track_get_sensor_data)
    i.track_init                             0x080046c0   Section        0  track.o(i.track_init)
    i.track_read_sensors                     0x080046cc   Section        0  track.o(i.track_read_sensors)
    i.usart_init                             0x080046f4   Section        0  usart.o(i.usart_init)
    i.usart_iqr_callback_register            0x08004850   Section        0  usart.o(i.usart_iqr_callback_register)
    i.usart_send_data                        0x0800485c   Section        0  usart.o(i.usart_send_data)
    i.usmart_cmd_rec                         0x080048e4   Section        0  usmart.o(i.usmart_cmd_rec)
    i.usmart_exe                             0x08004994   Section        0  usmart.o(i.usmart_exe)
    i.usmart_get_aparm                       0x08004c14   Section        0  usmart_str.o(i.usmart_get_aparm)
    i.usmart_get_cmdname                     0x08004cca   Section        0  usmart_str.o(i.usmart_get_cmdname)
    i.usmart_get_fname                       0x08004d0c   Section        0  usmart_str.o(i.usmart_get_fname)
    i.usmart_get_fparam                      0x08004ec0   Section        0  usmart_str.o(i.usmart_get_fparam)
    i.usmart_get_input_string                0x080051c8   Section        0  usmart_port.o(i.usmart_get_input_string)
    i.usmart_get_parmpos                     0x080051e4   Section        0  usmart_str.o(i.usmart_get_parmpos)
    i.usmart_init                            0x0800523c   Section        0  usmart.o(i.usmart_init)
    i.usmart_recv_func                       0x0800525c   Section        0  usmart_port.o(i.usmart_recv_func)
    i.usmart_scan                            0x080052a0   Section        0  usmart.o(i.usmart_scan)
    i.usmart_str2num                         0x08005344   Section        0  usmart_str.o(i.usmart_str2num)
    i.usmart_strcmp                          0x08005456   Section        0  usmart_str.o(i.usmart_strcmp)
    i.usmart_sys_cmd_exe                     0x08005470   Section        0  usmart.o(i.usmart_sys_cmd_exe)
    i.usmart_timx_get_time                   0x08005b0c   Section        0  usmart_port.o(i.usmart_timx_get_time)
    i.usmart_timx_init                       0x08005b40   Section        0  usmart_port.o(i.usmart_timx_init)
    i.usmart_timx_reset_time                 0x08005b98   Section        0  usmart_port.o(i.usmart_timx_reset_time)
    i.uxListRemove                           0x08005bc8   Section        0  list.o(i.uxListRemove)
    i.vListInitialise                        0x08005bec   Section        0  list.o(i.vListInitialise)
    i.vListInitialiseItem                    0x08005c02   Section        0  list.o(i.vListInitialiseItem)
    i.vListInsert                            0x08005c08   Section        0  list.o(i.vListInsert)
    i.vListInsertEnd                         0x08005c42   Section        0  list.o(i.vListInsertEnd)
    i.vPortEnterCritical                     0x08005c5c   Section        0  port.o(i.vPortEnterCritical)
    i.vPortExitCritical                      0x08005c78   Section        0  port.o(i.vPortExitCritical)
    i.vPortFree                              0x08005c90   Section        0  heap_4.o(i.vPortFree)
    i.vPortSetupTimerInterrupt               0x08005d1c   Section        0  port.o(i.vPortSetupTimerInterrupt)
    i.vQueueWaitForMessageRestricted         0x08005d34   Section        0  queue.o(i.vQueueWaitForMessageRestricted)
    i.vTaskDelay                             0x08005d80   Section        0  tasks.o(i.vTaskDelay)
    i.vTaskDelete                            0x08005db8   Section        0  tasks.o(i.vTaskDelete)
    i.vTaskInternalSetTimeOutState           0x08005e88   Section        0  tasks.o(i.vTaskInternalSetTimeOutState)
    i.vTaskListTasks                         0x08005e98   Section        0  tasks.o(i.vTaskListTasks)
    i.vTaskMissedYield                       0x0800601c   Section        0  tasks.o(i.vTaskMissedYield)
    i.vTaskPlaceOnEventList                  0x08006028   Section        0  tasks.o(i.vTaskPlaceOnEventList)
    i.vTaskPlaceOnEventListRestricted        0x08006048   Section        0  tasks.o(i.vTaskPlaceOnEventListRestricted)
    i.vTaskStartScheduler                    0x08006088   Section        0  tasks.o(i.vTaskStartScheduler)
    i.vTaskSuspendAll                        0x080060fc   Section        0  tasks.o(i.vTaskSuspendAll)
    i.vTaskSwitchContext                     0x0800610c   Section        0  tasks.o(i.vTaskSwitchContext)
    i.write_addr                             0x08006158   Section        0  usmart.o(i.write_addr)
    i.xPortGetFreeHeapSize                   0x0800615c   Section        0  heap_4.o(i.xPortGetFreeHeapSize)
    i.xPortStartScheduler                    0x08006168   Section        0  port.o(i.xPortStartScheduler)
    i.xPortSysTickHandler                    0x080061a8   Section        0  port.o(i.xPortSysTickHandler)
    i.xQueueGenericCreate                    0x080061d4   Section        0  queue.o(i.xQueueGenericCreate)
    i.xQueueReceive                          0x08006268   Section        0  queue.o(i.xQueueReceive)
    i.xTaskCheckForTimeOut                   0x0800637c   Section        0  tasks.o(i.xTaskCheckForTimeOut)
    i.xTaskCreate                            0x080063d4   Section        0  tasks.o(i.xTaskCreate)
    i.xTaskDelayUntil                        0x08006578   Section        0  tasks.o(i.xTaskDelayUntil)
    i.xTaskGetSchedulerState                 0x080065d4   Section        0  tasks.o(i.xTaskGetSchedulerState)
    i.xTaskGetTickCount                      0x080065f0   Section        0  tasks.o(i.xTaskGetTickCount)
    i.xTaskIncrementTick                     0x080065fc   Section        0  tasks.o(i.xTaskIncrementTick)
    i.xTaskRemoveFromEventList               0x08006774   Section        0  tasks.o(i.xTaskRemoveFromEventList)
    i.xTaskResumeAll                         0x0800685c   Section        0  tasks.o(i.xTaskResumeAll)
    i.xTimerCreateTimerTask                  0x080069b0   Section        0  timers.o(i.xTimerCreateTimerTask)
    .constdata                               0x08006a20   Section     1152  gpio.o(.constdata)
    .constdata                               0x08006ea0   Section      301  adc.o(.constdata)
    __func__                                 0x08006fc0   Data          13  adc.o(.constdata)
    .constdata                               0x08006fcd   Section       26  tim.o(.constdata)
    __func__                                 0x08006fcd   Data          26  tim.o(.constdata)
    .constdata                               0x08006fe7   Section        8  led.o(.constdata)
    .constdata                               0x08006fef   Section     1520  oled_data.o(.constdata)
    .constdata                               0x080075df   Section      570  oled_data.o(.constdata)
    .constdata                               0x08007819   Section      245  oled_data.o(.constdata)
    .constdata                               0x0800790e   Section       18  gpio_key.o(.constdata)
    .constdata                               0x08007920   Section        6  encoder.o(.constdata)
    .constdata                               0x08007928   Section       12  malloc.o(.constdata)
    .conststring                             0x08007934   Section       40  usmart.o(.conststring)
    .conststring                             0x0800795c   Section      176  usmart_config.o(.conststring)
    .data                                    0x20000000   Section        8  sys.o(.data)
    .data                                    0x20000008   Section        4  delay.o(.data)
    g_fac_us                                 0x20000008   Data           4  delay.o(.data)
    .data                                    0x2000000c   Section        8  i2c.o(.data)
    sda_pin                                  0x2000000c   Data           4  i2c.o(.data)
    scl_pin                                  0x20000010   Data           4  i2c.o(.data)
    .data                                    0x20000014   Section        1  adc.o(.data)
    .data                                    0x20000016   Section        4  tim.o(.data)
    count                                    0x20000018   Data           2  tim.o(.data)
    .data                                    0x2000001a   Section       10  gpio_key.o(.data)
    last_key                                 0x2000001a   Data           1  gpio_key.o(.data)
    debounce_count                           0x2000001b   Data           1  gpio_key.o(.data)
    click_count                              0x2000001c   Data           1  gpio_key.o(.data)
    pending_key                              0x2000001d   Data           1  gpio_key.o(.data)
    multi_click_timer                        0x2000001e   Data           1  gpio_key.o(.data)
    key_state                                0x2000001f   Data           2  gpio_key.o(.data)
    press_duration                           0x20000022   Data           2  gpio_key.o(.data)
    .data                                    0x20000024   Section        1  track.o(.data)
    .data                                    0x20000025   Section        1  app_key.o(.data)
    is_app_key_task_create                   0x20000025   Data           1  app_key.o(.data)
    .data                                    0x20000026   Section        2  app_state.o(.data)
    .data                                    0x20000028   Section        1  app_ui.o(.data)
    .data                                    0x2000002c   Section       20  malloc.o(.data)
    .data                                    0x20000040   Section       28  usmart.o(.data)
    .data                                    0x2000005c   Section       40  usmart_config.o(.data)
    .data                                    0x20000084   Section      244  usmart_config.o(.data)
    .data                                    0x20000178   Section        2  usmart_port.o(.data)
    .data                                    0x2000017c   Section       28  heap_4.o(.data)
    pxEnd                                    0x2000017c   Data           4  heap_4.o(.data)
    xFreeBytesRemaining                      0x20000180   Data           4  heap_4.o(.data)
    xMinimumEverFreeBytesRemaining           0x20000184   Data           4  heap_4.o(.data)
    xNumberOfSuccessfulAllocations           0x20000188   Data           4  heap_4.o(.data)
    xNumberOfSuccessfulFrees                 0x2000018c   Data           4  heap_4.o(.data)
    xStart                                   0x20000190   Data           8  heap_4.o(.data)
    .data                                    0x20000198   Section        4  port.o(.data)
    uxCriticalNesting                        0x20000198   Data           4  port.o(.data)
    .data                                    0x2000019c   Section       64  tasks.o(.data)
    uxDeletedTasksWaitingCleanUp             0x200001a0   Data           4  tasks.o(.data)
    uxCurrentNumberOfTasks                   0x200001a4   Data           4  tasks.o(.data)
    xTickCount                               0x200001a8   Data           4  tasks.o(.data)
    uxTopReadyPriority                       0x200001ac   Data           4  tasks.o(.data)
    xSchedulerRunning                        0x200001b0   Data           4  tasks.o(.data)
    xPendedTicks                             0x200001b4   Data           4  tasks.o(.data)
    xYieldPendings                           0x200001b8   Data           4  tasks.o(.data)
    xNumOfOverflows                          0x200001bc   Data           4  tasks.o(.data)
    uxTaskNumber                             0x200001c0   Data           4  tasks.o(.data)
    xNextTaskUnblockTime                     0x200001c4   Data           4  tasks.o(.data)
    uxTopUsedPriority                        0x200001c8   Data           4  tasks.o(.data)
    uxSchedulerSuspended                     0x200001cc   Data           4  tasks.o(.data)
    pxDelayedTaskList                        0x200001d0   Data           4  tasks.o(.data)
    pxOverflowDelayedTaskList                0x200001d4   Data           4  tasks.o(.data)
    xIdleTaskHandles                         0x200001d8   Data           4  tasks.o(.data)
    .data                                    0x200001dc   Section       20  timers.o(.data)
    xTimerQueue                              0x200001dc   Data           4  timers.o(.data)
    xTimerTaskHandle                         0x200001e0   Data           4  timers.o(.data)
    xLastTime                                0x200001e4   Data           4  timers.o(.data)
    pxCurrentTimerList                       0x200001e8   Data           4  timers.o(.data)
    pxOverflowTimerList                      0x200001ec   Data           4  timers.o(.data)
    .bss                                     0x200001f0   Section       64  gpio.o(.bss)
    .bss                                     0x20000230   Section       28  usart.o(.bss)
    .bss                                     0x2000024c   Section       48  adc.o(.bss)
    adc_pin_value_map                        0x2000024c   Data          16  adc.o(.bss)
    adc_value                                0x2000025c   Data          32  adc.o(.bss)
    .bss                                     0x2000027c   Section      228  tim.o(.bss)
    .bss                                     0x20000360   Section     1056  debug.o(.bss)
    debug_time_str                           0x20000760   Data          32  debug.o(.bss)
    .bss                                     0x20000780   Section     1024  oled.o(.bss)
    .bss                                     0x20000b80   Section      256  app_state.o(.bss)
    .bss                                     0x20000c80   Section    39232  malloc.o(.bss)
    mem1base                                 0x20000c80   Data       39232  malloc.o(.bss)
    .bss                                     0x2000a5c0   Section     2452  malloc.o(.bss)
    mem1mapbase                              0x2000a5c0   Data        2452  malloc.o(.bss)
    .bss                                     0x2000af54   Section      256  usmart_port.o(.bss)
    .bss                                     0x2000b054   Section    20480  heap_4.o(.bss)
    ucHeap                                   0x2000b054   Data       20480  heap_4.o(.bss)
    .bss                                     0x20010054   Section      180  tasks.o(.bss)
    pxReadyTasksLists                        0x20010054   Data         100  tasks.o(.bss)
    xDelayedTaskList1                        0x200100b8   Data          20  tasks.o(.bss)
    xDelayedTaskList2                        0x200100cc   Data          20  tasks.o(.bss)
    xPendingReadyList                        0x200100e0   Data          20  tasks.o(.bss)
    xTasksWaitingTermination                 0x200100f4   Data          20  tasks.o(.bss)
    .bss                                     0x20010108   Section       40  timers.o(.bss)
    xActiveTimerList1                        0x20010108   Data          20  timers.o(.bss)
    xActiveTimerList2                        0x2001011c   Data          20  timers.o(.bss)
    STACK                                    0x20010130   Section     1024  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000189   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x0800018d   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000191   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000191   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000191   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000191   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x08000199   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x0800019d   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x0800019d   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    vPortSVCHandler                          0x080001a1   Thumb Code    28  port.o(.emb_text)
    __asm___6_port_c_39a90d8d__prvStartFirstTask 0x080001c1   Thumb Code    36  port.o(.emb_text)
    __asm___6_port_c_39a90d8d__prvEnableVFP  0x080001e9   Thumb Code    16  port.o(.emb_text)
    xPortPendSVHandler                       0x080001fd   Thumb Code    88  port.o(.emb_text)
    vPortGetIPSR                             0x08000259   Thumb Code     6  port.o(.emb_text)
    Reset_Handler                            0x08000261   Thumb Code    14  startup_stm32f407xx.o(.text)
    NMI_Handler                              0x0800026f   Thumb Code     2  startup_stm32f407xx.o(.text)
    MemManage_Handler                        0x08000273   Thumb Code     2  startup_stm32f407xx.o(.text)
    BusFault_Handler                         0x08000275   Thumb Code     2  startup_stm32f407xx.o(.text)
    UsageFault_Handler                       0x08000277   Thumb Code     2  startup_stm32f407xx.o(.text)
    DebugMon_Handler                         0x0800027b   Thumb Code     2  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    __aeabi_memcpy                           0x0800028d   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x0800028d   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x0800028d   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x080002b1   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x080002b1   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x080002b1   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x080002bf   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x080002bf   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x080002bf   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x080002c3   Thumb Code    18  memseta.o(.text)
    strlen                                   0x080002d5   Thumb Code    14  strlen.o(.text)
    strcmp                                   0x080002e3   Thumb Code    28  strcmp.o(.text)
    strcpy                                   0x080002ff   Thumb Code    18  strcpy.o(.text)
    __aeabi_dadd                             0x08000311   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x08000453   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x08000459   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x0800045f   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x08000543   Thumb Code   222  ddiv.o(.text)
    __aeabi_cdrcmple                         0x08000621   Thumb Code    48  cdrcmple.o(.text)
    __aeabi_uidiv                            0x08000651   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x08000651   Thumb Code    44  uidiv.o(.text)
    __aeabi_uldivmod                         0x0800067d   Thumb Code    98  uldiv.o(.text)
    __aeabi_llsl                             0x080006df   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x080006df   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x080006fd   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x080006fd   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x0800071d   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x0800071d   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x08000741   Thumb Code     0  iusefp.o(.text)
    _double_round                            0x08000741   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x0800075f   Thumb Code   156  depilogue.o(.text)
    __aeabi_d2ulz                            0x080007fb   Thumb Code    48  dfixul.o(.text)
    __scatterload                            0x0800082d   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x0800082d   Thumb Code     0  init.o(.text)
    __decompress                             0x08000851   Thumb Code     0  __dczerorl2.o(.text)
    __decompress1                            0x08000851   Thumb Code    86  __dczerorl2.o(.text)
    EXTI0_IRQHandler                         0x080008a9   Thumb Code    32  gpio.o(i.EXTI0_IRQHandler)
    EXTI15_10_IRQHandler                     0x080008d1   Thumb Code   158  gpio.o(i.EXTI15_10_IRQHandler)
    EXTI1_IRQHandler                         0x08000979   Thumb Code    32  gpio.o(i.EXTI1_IRQHandler)
    EXTI2_IRQHandler                         0x080009a1   Thumb Code    32  gpio.o(i.EXTI2_IRQHandler)
    EXTI3_IRQHandler                         0x080009c9   Thumb Code    32  gpio.o(i.EXTI3_IRQHandler)
    EXTI4_IRQHandler                         0x080009f1   Thumb Code    32  gpio.o(i.EXTI4_IRQHandler)
    EXTI9_5_IRQHandler                       0x08000a19   Thumb Code   134  gpio.o(i.EXTI9_5_IRQHandler)
    HardFault_Handler                        0x08000aa9   Thumb Code    42  sys.o(i.HardFault_Handler)
    OLED_Init                                0x08000b15   Thumb Code   618  oled.o(i.OLED_Init)
    OLED_Printf                              0x08000d85   Thumb Code    42  oled.o(i.OLED_Printf)
    OLED_ShowChar                            0x08000db1   Thumb Code    56  oled.o(i.OLED_ShowChar)
    OLED_ShowImage                           0x08000df1   Thumb Code   308  oled.o(i.OLED_ShowImage)
    OLED_ShowString                          0x08000f29   Thumb Code   250  oled.o(i.OLED_ShowString)
    OLED_Update                              0x0800102d   Thumb Code   110  oled.o(i.OLED_Update)
    PendSV_Handler                           0x080010a1   Thumb Code     4  sys.o(i.PendSV_Handler)
    SVC_Handler                              0x080010a5   Thumb Code     4  sys.o(i.SVC_Handler)
    SysTick_Handler                          0x080010a9   Thumb Code    36  sys.o(i.SysTick_Handler)
    TIM1_BRK_TIM9_IRQHandler                 0x080010d1   Thumb Code    16  tim.o(i.TIM1_BRK_TIM9_IRQHandler)
    TIM1_CC_IRQHandler                       0x080010e5   Thumb Code    16  tim.o(i.TIM1_CC_IRQHandler)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080010f9   Thumb Code    16  tim.o(i.TIM1_TRG_COM_TIM11_IRQHandler)
    TIM1_UP_TIM10_IRQHandler                 0x0800110d   Thumb Code    16  tim.o(i.TIM1_UP_TIM10_IRQHandler)
    TIM2_IRQHandler                          0x08001121   Thumb Code    16  tim.o(i.TIM2_IRQHandler)
    TIM3_IRQHandler                          0x08001135   Thumb Code    16  tim.o(i.TIM3_IRQHandler)
    TIM4_IRQHandler                          0x08001149   Thumb Code    42  usmart_port.o(i.TIM4_IRQHandler)
    TIM5_IRQHandler                          0x08001185   Thumb Code    16  tim.o(i.TIM5_IRQHandler)
    TIM6_DAC_IRQHandler                      0x08001199   Thumb Code    84  tim.o(i.TIM6_DAC_IRQHandler)
    TIM7_IRQHandler                          0x080011f9   Thumb Code    16  tim.o(i.TIM7_IRQHandler)
    TIM8_BRK_TIM12_IRQHandler                0x0800120d   Thumb Code    16  tim.o(i.TIM8_BRK_TIM12_IRQHandler)
    TIM8_CC_IRQHandler                       0x08001221   Thumb Code    16  tim.o(i.TIM8_CC_IRQHandler)
    TIM8_TRG_COM_TIM14_IRQHandler            0x08001235   Thumb Code    16  tim.o(i.TIM8_TRG_COM_TIM14_IRQHandler)
    TIM8_UP_TIM13_IRQHandler                 0x08001249   Thumb Code    16  tim.o(i.TIM8_UP_TIM13_IRQHandler)
    UART4_IRQHandler                         0x0800125d   Thumb Code    30  usart.o(i.UART4_IRQHandler)
    UART5_IRQHandler                         0x08001289   Thumb Code    28  usart.o(i.UART5_IRQHandler)
    USART1_IRQHandler                        0x080012ad   Thumb Code    28  usart.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x080012d1   Thumb Code    30  usart.o(i.USART2_IRQHandler)
    USART3_IRQHandler                        0x080012fd   Thumb Code    30  usart.o(i.USART3_IRQHandler)
    USART6_IRQHandler                        0x08001329   Thumb Code    30  usart.o(i.USART6_IRQHandler)
    __0snprintf                              0x08001355   Thumb Code    48  printfa.o(i.__0snprintf)
    __1snprintf                              0x08001355   Thumb Code     0  printfa.o(i.__0snprintf)
    __2snprintf                              0x08001355   Thumb Code     0  printfa.o(i.__0snprintf)
    __c89snprintf                            0x08001355   Thumb Code     0  printfa.o(i.__0snprintf)
    snprintf                                 0x08001355   Thumb Code     0  printfa.o(i.__0snprintf)
    __0vsnprintf                             0x08001389   Thumb Code    46  printfa.o(i.__0vsnprintf)
    __1vsnprintf                             0x08001389   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __2vsnprintf                             0x08001389   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __c89vsnprintf                           0x08001389   Thumb Code     0  printfa.o(i.__0vsnprintf)
    vsnprintf                                0x08001389   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __0vsprintf                              0x080013bd   Thumb Code    30  printfa.o(i.__0vsprintf)
    __1vsprintf                              0x080013bd   Thumb Code     0  printfa.o(i.__0vsprintf)
    __2vsprintf                              0x080013bd   Thumb Code     0  printfa.o(i.__0vsprintf)
    __c89vsprintf                            0x080013bd   Thumb Code     0  printfa.o(i.__0vsprintf)
    vsprintf                                 0x080013bd   Thumb Code     0  printfa.o(i.__0vsprintf)
    __ARM_common_memclr4_10                  0x080013e1   Thumb Code    10  usmart_str.o(i.__ARM_common_memclr4_10)
    __scatterload_copy                       0x080013eb   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x080013f9   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x080013fb   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    adc_pin_init                             0x08001cb5   Thumb Code   540  adc.o(i.adc_pin_init)
    app_init                                 0x08001f39   Thumb Code    76  main.o(i.app_init)
    app_key_task_start                       0x080020dd   Thumb Code    48  app_key.o(i.app_key_task_start)
    app_state_task_start                     0x08002275   Thumb Code    48  app_state.o(i.app_state_task_start)
    app_ui_task_start                        0x0800232d   Thumb Code    48  app_ui.o(i.app_ui_task_start)
    debug_error                              0x08002371   Thumb Code    98  debug.o(i.debug_error)
    debug_init                               0x080023e9   Thumb Code   142  debug.o(i.debug_init)
    debug_printf                             0x080024c9   Thumb Code    88  debug.o(i.debug_printf)
    delay_init                               0x08002529   Thumb Code    32  delay.o(i.delay_init)
    delay_ms                                 0x0800254d   Thumb Code    52  delay.o(i.delay_ms)
    delay_us                                 0x08002585   Thumb Code    46  delay.o(i.delay_us)
    dma_basic_config                         0x080025b9   Thumb Code    64  dma.o(i.dma_basic_config)
    dma_enable                               0x08002601   Thumb Code    28  dma.o(i.dma_enable)
    encoder_init                             0x0800261d   Thumb Code    54  encoder.o(i.encoder_init)
    get_debug_time                           0x08002659   Thumb Code    78  debug.o(i.get_debug_time)
    gpio_af_set                              0x080026c9   Thumb Code   102  gpio.o(i.gpio_af_set)
    gpio_key_get_state                       0x08002735   Thumb Code    20  gpio_key.o(i.gpio_key_get_state)
    gpio_key_init                            0x0800274d   Thumb Code    66  gpio_key.o(i.gpio_key_init)
    gpio_key_scan                            0x08002795   Thumb Code   280  gpio_key.o(i.gpio_key_scan)
    gpio_nvic_ex_config                      0x080028bd   Thumb Code   270  gpio.o(i.gpio_nvic_ex_config)
    gpio_pin_init                            0x080029e5   Thumb Code   384  gpio.o(i.gpio_pin_init)
    gpio_read_pin                            0x08002b6d   Thumb Code    22  gpio.o(i.gpio_read_pin)
    gpio_toggle_pin                          0x08002b89   Thumb Code    20  gpio.o(i.gpio_toggle_pin)
    gpio_write_pin                           0x08002ba1   Thumb Code    30  gpio.o(i.gpio_write_pin)
    i2c_init                                 0x08002bc5   Thumb Code    44  i2c.o(i.i2c_init)
    i2c_read_data                            0x08002bf5   Thumb Code   478  i2c.o(i.i2c_read_data)
    i2c_send_byte                            0x08002e69   Thumb Code    82  i2c.o(i.i2c_send_byte)
    i2c_stop                                 0x08002ec1   Thumb Code    54  i2c.o(i.i2c_stop)
    i2c_wait_ack                             0x08002efd   Thumb Code   134  i2c.o(i.i2c_wait_ack)
    i2c_write_data                           0x08002f89   Thumb Code   270  i2c.o(i.i2c_write_data)
    key_get_value                            0x0800309d   Thumb Code     4  key.o(i.key_get_value)
    key_init                                 0x080030a1   Thumb Code    18  key.o(i.key_init)
    key_scan                                 0x080030b9   Thumb Code     4  key.o(i.key_scan)
    led_init                                 0x080030bd   Thumb Code    52  led.o(i.led_init)
    led_test                                 0x080030f5   Thumb Code     6  main.o(i.led_test)
    led_toggle                               0x080030fd   Thumb Code    12  led.o(i.led_toggle)
    main                                     0x0800310d   Thumb Code   226  main.o(i.main)
    my_mem_init                              0x08003241   Thumb Code    48  malloc.o(i.my_mem_init)
    my_mem_perused                           0x08003279   Thumb Code    52  malloc.o(i.my_mem_perused)
    my_printf                                0x080032b5   Thumb Code    70  debug.o(i.my_printf)
    pvPortMalloc                             0x0800397d   Thumb Code   236  heap_4.o(i.pvPortMalloc)
    pxPortInitialiseStack                    0x08003a75   Thumb Code    38  port.o(i.pxPortInitialiseStack)
    read_addr                                0x08003aa1   Thumb Code     4  usmart.o(i.read_addr)
    sys_check_rst                            0x08003aa5   Thumb Code   118  sys.o(i.sys_check_rst)
    sys_clock_set                            0x08003bd1   Thumb Code   270  sys.o(i.sys_clock_set)
    sys_get_tick                             0x08003cf9   Thumb Code     8  sys.o(i.sys_get_tick)
    sys_nvic_init                            0x08003d05   Thumb Code   106  sys.o(i.sys_nvic_init)
    sys_soft_reset                           0x08003d75   Thumb Code     8  sys.o(i.sys_soft_reset)
    sys_stm32_clock_init                     0x08003d85   Thumb Code    56  sys.o(i.sys_stm32_clock_init)
    task_test                                0x08003dcd   Thumb Code    52  main.o(i.task_test)
    tim_cnt_pin_init                         0x08003f7d   Thumb Code   570  tim.o(i.tim_cnt_pin_init)
    tim_cnt_set_value                        0x080041f1   Thumb Code   116  tim.o(i.tim_cnt_set_value)
    tim_int_callback_deregister              0x08004299   Thumb Code   112  tim.o(i.tim_int_callback_deregister)
    tim_int_callback_register                0x08004315   Thumb Code   108  tim.o(i.tim_int_callback_register)
    tim_pwm_pin_init                         0x080043cd   Thumb Code   492  tim.o(i.tim_pwm_pin_init)
    tim_pwm_set_ccr                          0x080045f1   Thumb Code   144  tim.o(i.tim_pwm_set_ccr)
    track_get_sensor_data                    0x080046b5   Thumb Code     6  track.o(i.track_get_sensor_data)
    track_init                               0x080046c1   Thumb Code    10  track.o(i.track_init)
    track_read_sensors                       0x080046cd   Thumb Code    34  track.o(i.track_read_sensors)
    usart_init                               0x080046f5   Thumb Code   316  usart.o(i.usart_init)
    usart_iqr_callback_register              0x08004851   Thumb Code     8  usart.o(i.usart_iqr_callback_register)
    usart_send_data                          0x0800485d   Thumb Code   110  usart.o(i.usart_send_data)
    usmart_cmd_rec                           0x080048e5   Thumb Code   172  usmart.o(i.usmart_cmd_rec)
    usmart_exe                               0x08004995   Thumb Code   538  usmart.o(i.usmart_exe)
    usmart_get_aparm                         0x08004c15   Thumb Code   182  usmart_str.o(i.usmart_get_aparm)
    usmart_get_cmdname                       0x08004ccb   Thumb Code    66  usmart_str.o(i.usmart_get_cmdname)
    usmart_get_fname                         0x08004d0d   Thumb Code   426  usmart_str.o(i.usmart_get_fname)
    usmart_get_fparam                        0x08004ec1   Thumb Code   768  usmart_str.o(i.usmart_get_fparam)
    usmart_get_input_string                  0x080051c9   Thumb Code    20  usmart_port.o(i.usmart_get_input_string)
    usmart_get_parmpos                       0x080051e5   Thumb Code    84  usmart_str.o(i.usmart_get_parmpos)
    usmart_init                              0x0800523d   Thumb Code    24  usmart.o(i.usmart_init)
    usmart_recv_func                         0x0800525d   Thumb Code    60  usmart_port.o(i.usmart_recv_func)
    usmart_scan                              0x080052a1   Thumb Code   104  usmart.o(i.usmart_scan)
    usmart_str2num                           0x08005345   Thumb Code   274  usmart_str.o(i.usmart_str2num)
    usmart_strcmp                            0x08005457   Thumb Code    26  usmart_str.o(i.usmart_strcmp)
    usmart_sys_cmd_exe                       0x08005471   Thumb Code  1488  usmart.o(i.usmart_sys_cmd_exe)
    usmart_timx_get_time                     0x08005b0d   Thumb Code    38  usmart_port.o(i.usmart_timx_get_time)
    usmart_timx_init                         0x08005b41   Thumb Code    70  usmart_port.o(i.usmart_timx_init)
    usmart_timx_reset_time                   0x08005b99   Thumb Code    32  usmart_port.o(i.usmart_timx_reset_time)
    uxListRemove                             0x08005bc9   Thumb Code    36  list.o(i.uxListRemove)
    vListInitialise                          0x08005bed   Thumb Code    22  list.o(i.vListInitialise)
    vListInitialiseItem                      0x08005c03   Thumb Code     6  list.o(i.vListInitialiseItem)
    vListInsert                              0x08005c09   Thumb Code    58  list.o(i.vListInsert)
    vListInsertEnd                           0x08005c43   Thumb Code    24  list.o(i.vListInsertEnd)
    vPortEnterCritical                       0x08005c5d   Thumb Code    24  port.o(i.vPortEnterCritical)
    vPortExitCritical                        0x08005c79   Thumb Code    20  port.o(i.vPortExitCritical)
    vPortFree                                0x08005c91   Thumb Code   136  heap_4.o(i.vPortFree)
    vPortSetupTimerInterrupt                 0x08005d1d   Thumb Code    20  port.o(i.vPortSetupTimerInterrupt)
    vQueueWaitForMessageRestricted           0x08005d35   Thumb Code    74  queue.o(i.vQueueWaitForMessageRestricted)
    vTaskDelay                               0x08005d81   Thumb Code    46  tasks.o(i.vTaskDelay)
    vTaskDelete                              0x08005db9   Thumb Code   190  tasks.o(i.vTaskDelete)
    vTaskInternalSetTimeOutState             0x08005e89   Thumb Code    12  tasks.o(i.vTaskInternalSetTimeOutState)
    vTaskListTasks                           0x08005e99   Thumb Code   358  tasks.o(i.vTaskListTasks)
    vTaskMissedYield                         0x0800601d   Thumb Code     8  tasks.o(i.vTaskMissedYield)
    vTaskPlaceOnEventList                    0x08006029   Thumb Code    28  tasks.o(i.vTaskPlaceOnEventList)
    vTaskPlaceOnEventListRestricted          0x08006049   Thumb Code    60  tasks.o(i.vTaskPlaceOnEventListRestricted)
    vTaskStartScheduler                      0x08006089   Thumb Code    96  tasks.o(i.vTaskStartScheduler)
    vTaskSuspendAll                          0x080060fd   Thumb Code    10  tasks.o(i.vTaskSuspendAll)
    vTaskSwitchContext                       0x0800610d   Thumb Code    68  tasks.o(i.vTaskSwitchContext)
    write_addr                               0x08006159   Thumb Code     4  usmart.o(i.write_addr)
    xPortGetFreeHeapSize                     0x0800615d   Thumb Code     6  heap_4.o(i.xPortGetFreeHeapSize)
    xPortStartScheduler                      0x08006169   Thumb Code    52  port.o(i.xPortStartScheduler)
    xPortSysTickHandler                      0x080061a9   Thumb Code    38  port.o(i.xPortSysTickHandler)
    xQueueGenericCreate                      0x080061d5   Thumb Code   148  queue.o(i.xQueueGenericCreate)
    xQueueReceive                            0x08006269   Thumb Code   272  queue.o(i.xQueueReceive)
    xTaskCheckForTimeOut                     0x0800637d   Thumb Code    82  tasks.o(i.xTaskCheckForTimeOut)
    xTaskCreate                              0x080063d5   Thumb Code   390  tasks.o(i.xTaskCreate)
    xTaskDelayUntil                          0x08006579   Thumb Code    84  tasks.o(i.xTaskDelayUntil)
    xTaskGetSchedulerState                   0x080065d5   Thumb Code    24  tasks.o(i.xTaskGetSchedulerState)
    xTaskGetTickCount                        0x080065f1   Thumb Code     6  tasks.o(i.xTaskGetTickCount)
    xTaskIncrementTick                       0x080065fd   Thumb Code   362  tasks.o(i.xTaskIncrementTick)
    xTaskRemoveFromEventList                 0x08006775   Thumb Code   214  tasks.o(i.xTaskRemoveFromEventList)
    xTaskResumeAll                           0x0800685d   Thumb Code   322  tasks.o(i.xTaskResumeAll)
    xTimerCreateTimerTask                    0x080069b1   Thumb Code    84  timers.o(i.xTimerCreateTimerTask)
    gpio_map                                 0x08006a20   Data        1152  gpio.o(.constdata)
    adc_channel_map                          0x08006ea0   Data         288  adc.o(.constdata)
    led_pin                                  0x08006fe7   Data           4  led.o(.constdata)
    led_polarity                             0x08006feb   Data           4  led.o(.constdata)
    OLED_F8x16                               0x08006fef   Data        1520  oled_data.o(.constdata)
    OLED_F6x8                                0x080075df   Data         570  oled_data.o(.constdata)
    OLED_CF16x16                             0x08007819   Data         245  oled_data.o(.constdata)
    gpio_key_pin                             0x0800790e   Data           6  gpio_key.o(.constdata)
    gpio_key_value                           0x08007914   Data           6  gpio_key.o(.constdata)
    gpio_key_polarity                        0x0800791a   Data           6  gpio_key.o(.constdata)
    encoder_tim_id                           0x08007920   Data           2  encoder.o(.constdata)
    encoder_pin                              0x08007922   Data           4  encoder.o(.constdata)
    memtblsize                               0x08007928   Data           4  malloc.o(.constdata)
    memblksize                               0x0800792c   Data           4  malloc.o(.constdata)
    memsize                                  0x08007930   Data           4  malloc.o(.constdata)
    Region$$Table$$Base                      0x08007a0c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08007a2c   Number         0  anon$$obj.o(Region$$Table)
    uwTick                                   0x20000000   Data           8  sys.o(.data)
    adc_regular_rank_cnt                     0x20000014   Data           1  adc.o(.data)
    g_bitmx_callback_funs_num                0x20000016   Data           1  tim.o(.data)
    digital                                  0x20000024   Data           1  track.o(.data)
    is_app_state_task_start                  0x20000026   Data           1  app_state.o(.data)
    sys_state                                0x20000027   Data           1  app_state.o(.data)
    is_app_ui_task_start                     0x20000028   Data           1  app_ui.o(.data)
    mallco_dev                               0x2000002c   Data          20  malloc.o(.data)
    sys_cmd_tab                              0x20000040   Data          28  usmart.o(.data)
    usmart_nametab                           0x2000005c   Data          40  usmart_config.o(.data)
    usmart_dev                               0x20000084   Data         244  usmart_config.o(.data)
    usmart_recv_cnt                          0x20000178   Data           1  usmart_port.o(.data)
    usmart_recv_flag                         0x20000179   Data           1  usmart_port.o(.data)
    pxCurrentTCB                             0x2000019c   Data           4  tasks.o(.data)
    gpio_ex_callback_funs                    0x200001f0   Data          64  gpio.o(.bss)
    g_usart_callback_funs                    0x20000230   Data          28  usart.o(.bss)
    g_timxchy_cap_sta                        0x2000027c   Data          15  tim.o(.bss)
    g_timxchy_cap_channel                    0x2000028b   Data          15  tim.o(.bss)
    g_timxchy_cap_val                        0x2000029c   Data          60  tim.o(.bss)
    g_timxchy_cap_en                         0x200002d8   Data          15  tim.o(.bss)
    g_timx_callback_funs                     0x200002e8   Data          80  tim.o(.bss)
    g_bitmx_callback_funs_time               0x20000338   Data          40  tim.o(.bss)
    log_str                                  0x20000360   Data        1024  debug.o(.bss)
    OLED_DisplayBuf                          0x20000780   Data        1024  oled.o(.bss)
    task_log                                 0x20000b80   Data         256  app_state.o(.bss)
    usmart_recv_buf                          0x2000af54   Data         256  usmart_port.o(.bss)
    __initial_sp                             0x20010530   Data           0  startup_stm32f407xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00007c1c, Max: 0x00100000, ABSOLUTE, COMPRESSED[0x00007a98])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00007a2c, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000000   Code   RO         2877  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000188   0x08000188   0x00000004   Code   RO         3210    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x0800018c   0x0800018c   0x00000004   Code   RO         3213    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000190   0x08000190   0x00000000   Code   RO         3215    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000190   0x08000190   0x00000000   Code   RO         3217    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000190   0x08000190   0x00000008   Code   RO         3218    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000198   0x08000198   0x00000004   Code   RO         3225    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x0800019c   0x0800019c   0x00000000   Code   RO         3220    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x0800019c   0x0800019c   0x00000000   Code   RO         3222    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x0800019c   0x0800019c   0x00000004   Code   RO         3211    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001a0   0x080001a0   0x000000be   Code   RO         2175    .emb_text           port.o
    0x0800025e   0x0800025e   0x00000002   PAD
    0x08000260   0x08000260   0x0000002c   Code   RO            4    .text               startup_stm32f407xx.o
    0x0800028c   0x0800028c   0x00000024   Code   RO         2880    .text               mc_w.l(memcpya.o)
    0x080002b0   0x080002b0   0x00000024   Code   RO         2882    .text               mc_w.l(memseta.o)
    0x080002d4   0x080002d4   0x0000000e   Code   RO         2884    .text               mc_w.l(strlen.o)
    0x080002e2   0x080002e2   0x0000001c   Code   RO         2886    .text               mc_w.l(strcmp.o)
    0x080002fe   0x080002fe   0x00000012   Code   RO         2888    .text               mc_w.l(strcpy.o)
    0x08000310   0x08000310   0x0000014e   Code   RO         3153    .text               mf_w.l(dadd.o)
    0x0800045e   0x0800045e   0x000000e4   Code   RO         3155    .text               mf_w.l(dmul.o)
    0x08000542   0x08000542   0x000000de   Code   RO         3157    .text               mf_w.l(ddiv.o)
    0x08000620   0x08000620   0x00000030   Code   RO         3171    .text               mf_w.l(cdrcmple.o)
    0x08000650   0x08000650   0x0000002c   Code   RO         3227    .text               mc_w.l(uidiv.o)
    0x0800067c   0x0800067c   0x00000062   Code   RO         3229    .text               mc_w.l(uldiv.o)
    0x080006de   0x080006de   0x0000001e   Code   RO         3231    .text               mc_w.l(llshl.o)
    0x080006fc   0x080006fc   0x00000020   Code   RO         3233    .text               mc_w.l(llushr.o)
    0x0800071c   0x0800071c   0x00000024   Code   RO         3235    .text               mc_w.l(llsshr.o)
    0x08000740   0x08000740   0x00000000   Code   RO         3244    .text               mc_w.l(iusefp.o)
    0x08000740   0x08000740   0x000000ba   Code   RO         3247    .text               mf_w.l(depilogue.o)
    0x080007fa   0x080007fa   0x00000030   Code   RO         3251    .text               mf_w.l(dfixul.o)
    0x0800082a   0x0800082a   0x00000002   PAD
    0x0800082c   0x0800082c   0x00000024   Code   RO         3257    .text               mc_w.l(init.o)
    0x08000850   0x08000850   0x00000056   Code   RO         3267    .text               mc_w.l(__dczerorl2.o)
    0x080008a6   0x080008a6   0x00000002   PAD
    0x080008a8   0x080008a8   0x00000028   Code   RO           13    i.EXTI0_IRQHandler  gpio.o
    0x080008d0   0x080008d0   0x000000a8   Code   RO           14    i.EXTI15_10_IRQHandler  gpio.o
    0x08000978   0x08000978   0x00000028   Code   RO           15    i.EXTI1_IRQHandler  gpio.o
    0x080009a0   0x080009a0   0x00000028   Code   RO           16    i.EXTI2_IRQHandler  gpio.o
    0x080009c8   0x080009c8   0x00000028   Code   RO           17    i.EXTI3_IRQHandler  gpio.o
    0x080009f0   0x080009f0   0x00000028   Code   RO           18    i.EXTI4_IRQHandler  gpio.o
    0x08000a18   0x08000a18   0x00000090   Code   RO           19    i.EXTI9_5_IRQHandler  gpio.o
    0x08000aa8   0x08000aa8   0x0000006c   Code   RO          190    i.HardFault_Handler  sys.o
    0x08000b14   0x08000b14   0x00000270   Code   RO          997    i.OLED_Init         oled.o
    0x08000d84   0x08000d84   0x0000002a   Code   RO         1000    i.OLED_Printf       oled.o
    0x08000dae   0x08000dae   0x00000002   PAD
    0x08000db0   0x08000db0   0x00000040   Code   RO         1005    i.OLED_ShowChar     oled.o
    0x08000df0   0x08000df0   0x00000138   Code   RO         1008    i.OLED_ShowImage    oled.o
    0x08000f28   0x08000f28   0x00000104   Code   RO         1011    i.OLED_ShowString   oled.o
    0x0800102c   0x0800102c   0x00000074   Code   RO         1012    i.OLED_Update       oled.o
    0x080010a0   0x080010a0   0x00000004   Code   RO          191    i.PendSV_Handler    sys.o
    0x080010a4   0x080010a4   0x00000004   Code   RO          192    i.SVC_Handler       sys.o
    0x080010a8   0x080010a8   0x00000028   Code   RO          193    i.SysTick_Handler   sys.o
    0x080010d0   0x080010d0   0x00000014   Code   RO          669    i.TIM1_BRK_TIM9_IRQHandler  tim.o
    0x080010e4   0x080010e4   0x00000014   Code   RO          670    i.TIM1_CC_IRQHandler  tim.o
    0x080010f8   0x080010f8   0x00000014   Code   RO          671    i.TIM1_TRG_COM_TIM11_IRQHandler  tim.o
    0x0800110c   0x0800110c   0x00000014   Code   RO          672    i.TIM1_UP_TIM10_IRQHandler  tim.o
    0x08001120   0x08001120   0x00000014   Code   RO          673    i.TIM2_IRQHandler   tim.o
    0x08001134   0x08001134   0x00000014   Code   RO          674    i.TIM3_IRQHandler   tim.o
    0x08001148   0x08001148   0x0000003c   Code   RO         1832    i.TIM4_IRQHandler   usmart_port.o
    0x08001184   0x08001184   0x00000014   Code   RO          675    i.TIM5_IRQHandler   tim.o
    0x08001198   0x08001198   0x00000060   Code   RO          676    i.TIM6_DAC_IRQHandler  tim.o
    0x080011f8   0x080011f8   0x00000014   Code   RO          677    i.TIM7_IRQHandler   tim.o
    0x0800120c   0x0800120c   0x00000014   Code   RO          678    i.TIM8_BRK_TIM12_IRQHandler  tim.o
    0x08001220   0x08001220   0x00000014   Code   RO          679    i.TIM8_CC_IRQHandler  tim.o
    0x08001234   0x08001234   0x00000014   Code   RO          680    i.TIM8_TRG_COM_TIM14_IRQHandler  tim.o
    0x08001248   0x08001248   0x00000014   Code   RO          681    i.TIM8_UP_TIM13_IRQHandler  tim.o
    0x0800125c   0x0800125c   0x0000002c   Code   RO          377    i.UART4_IRQHandler  usart.o
    0x08001288   0x08001288   0x00000024   Code   RO          378    i.UART5_IRQHandler  usart.o
    0x080012ac   0x080012ac   0x00000024   Code   RO          379    i.USART1_IRQHandler  usart.o
    0x080012d0   0x080012d0   0x0000002c   Code   RO          380    i.USART2_IRQHandler  usart.o
    0x080012fc   0x080012fc   0x0000002c   Code   RO          381    i.USART3_IRQHandler  usart.o
    0x08001328   0x08001328   0x0000002c   Code   RO          382    i.USART6_IRQHandler  usart.o
    0x08001354   0x08001354   0x00000034   Code   RO         3126    i.__0snprintf       mc_w.l(printfa.o)
    0x08001388   0x08001388   0x00000034   Code   RO         3130    i.__0vsnprintf      mc_w.l(printfa.o)
    0x080013bc   0x080013bc   0x00000024   Code   RO         3131    i.__0vsprintf       mc_w.l(printfa.o)
    0x080013e0   0x080013e0   0x0000000a   Code   RO         1960    i.__ARM_common_memclr4_10  usmart_str.o
    0x080013ea   0x080013ea   0x0000000e   Code   RO         3261    i.__scatterload_copy  mc_w.l(handlers.o)
    0x080013f8   0x080013f8   0x00000002   Code   RO         3262    i.__scatterload_null  mc_w.l(handlers.o)
    0x080013fa   0x080013fa   0x0000000e   Code   RO         3263    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08001408   0x08001408   0x00000184   Code   RO         3132    i._fp_digits        mc_w.l(printfa.o)
    0x0800158c   0x0800158c   0x000006b4   Code   RO         3133    i._printf_core      mc_w.l(printfa.o)
    0x08001c40   0x08001c40   0x00000024   Code   RO         3134    i._printf_post_padding  mc_w.l(printfa.o)
    0x08001c64   0x08001c64   0x0000002e   Code   RO         3135    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08001c92   0x08001c92   0x00000016   Code   RO         3136    i._snputc           mc_w.l(printfa.o)
    0x08001ca8   0x08001ca8   0x0000000a   Code   RO         3137    i._sputc            mc_w.l(printfa.o)
    0x08001cb2   0x08001cb2   0x00000002   PAD
    0x08001cb4   0x08001cb4   0x00000284   Code   RO          634    i.adc_pin_init      adc.o
    0x08001f38   0x08001f38   0x00000134   Code   RO         1444    i.app_init          main.o
    0x0800206c   0x0800206c   0x00000070   Code   RO         1525    i.app_key_task      app_key.o
    0x080020dc   0x080020dc   0x0000004c   Code   RO         1526    i.app_key_task_start  app_key.o
    0x08002128   0x08002128   0x0000014c   Code   RO         1584    i.app_state_task    app_state.o
    0x08002274   0x08002274   0x00000048   Code   RO         1585    i.app_state_task_start  app_state.o
    0x080022bc   0x080022bc   0x00000070   Code   RO         1626    i.app_ui_task       app_ui.o
    0x0800232c   0x0800232c   0x00000044   Code   RO         1627    i.app_ui_task_start  app_ui.o
    0x08002370   0x08002370   0x00000078   Code   RO          909    i.debug_error       debug.o
    0x080023e8   0x080023e8   0x000000e0   Code   RO          910    i.debug_init        debug.o
    0x080024c8   0x080024c8   0x00000060   Code   RO          912    i.debug_printf      debug.o
    0x08002528   0x08002528   0x00000024   Code   RO          339    i.delay_init        delay.o
    0x0800254c   0x0800254c   0x00000038   Code   RO          340    i.delay_ms          delay.o
    0x08002584   0x08002584   0x00000034   Code   RO          341    i.delay_us          delay.o
    0x080025b8   0x080025b8   0x00000048   Code   RO          556    i.dma_basic_config  dma.o
    0x08002600   0x08002600   0x0000001c   Code   RO          557    i.dma_enable        dma.o
    0x0800261c   0x0800261c   0x0000003c   Code   RO         1359    i.encoder_init      encoder.o
    0x08002658   0x08002658   0x00000070   Code   RO          915    i.get_debug_time    debug.o
    0x080026c8   0x080026c8   0x0000006c   Code   RO           20    i.gpio_af_set       gpio.o
    0x08002734   0x08002734   0x00000018   Code   RO         1237    i.gpio_key_get_state  gpio_key.o
    0x0800274c   0x0800274c   0x00000048   Code   RO         1238    i.gpio_key_init     gpio_key.o
    0x08002794   0x08002794   0x00000128   Code   RO         1240    i.gpio_key_scan     gpio_key.o
    0x080028bc   0x080028bc   0x00000128   Code   RO           23    i.gpio_nvic_ex_config  gpio.o
    0x080029e4   0x080029e4   0x00000188   Code   RO           24    i.gpio_pin_init     gpio.o
    0x08002b6c   0x08002b6c   0x0000001c   Code   RO           25    i.gpio_read_pin     gpio.o
    0x08002b88   0x08002b88   0x00000018   Code   RO           26    i.gpio_toggle_pin   gpio.o
    0x08002ba0   0x08002ba0   0x00000024   Code   RO           27    i.gpio_write_pin    gpio.o
    0x08002bc4   0x08002bc4   0x00000030   Code   RO          477    i.i2c_init          i2c.o
    0x08002bf4   0x08002bf4   0x00000274   Code   RO          480    i.i2c_read_data     i2c.o
    0x08002e68   0x08002e68   0x00000058   Code   RO          481    i.i2c_send_byte     i2c.o
    0x08002ec0   0x08002ec0   0x0000003c   Code   RO          483    i.i2c_stop          i2c.o
    0x08002efc   0x08002efc   0x0000008c   Code   RO          484    i.i2c_wait_ack      i2c.o
    0x08002f88   0x08002f88   0x00000114   Code   RO          485    i.i2c_write_data    i2c.o
    0x0800309c   0x0800309c   0x00000004   Code   RO         1286    i.key_get_value     key.o
    0x080030a0   0x080030a0   0x00000018   Code   RO         1287    i.key_init          key.o
    0x080030b8   0x080030b8   0x00000004   Code   RO         1288    i.key_scan          key.o
    0x080030bc   0x080030bc   0x00000038   Code   RO          867    i.led_init          led.o
    0x080030f4   0x080030f4   0x00000006   Code   RO         1446    i.led_test          main.o
    0x080030fa   0x080030fa   0x00000002   PAD
    0x080030fc   0x080030fc   0x00000010   Code   RO          869    i.led_toggle        led.o
    0x0800310c   0x0800310c   0x00000134   Code   RO         1447    i.main              main.o
    0x08003240   0x08003240   0x00000038   Code   RO         1668    i.my_mem_init       malloc.o
    0x08003278   0x08003278   0x0000003c   Code   RO         1670    i.my_mem_perused    malloc.o
    0x080032b4   0x080032b4   0x0000004c   Code   RO          916    i.my_printf         debug.o
    0x08003300   0x08003300   0x00000060   Code   RO         2416    i.prvAddCurrentTaskToDelayedList  tasks.o
    0x08003360   0x08003360   0x00000064   Code   RO         2417    i.prvIdleTask       tasks.o
    0x080033c4   0x080033c4   0x00000100   Code   RO         2418    i.prvListTasksWithinSingleList  tasks.o
    0x080034c4   0x080034c4   0x000000a0   Code   RO         2730    i.prvProcessExpiredTimer  timers.o
    0x08003564   0x08003564   0x000000d4   Code   RO         2731    i.prvSampleTimeNow  timers.o
    0x08003638   0x08003638   0x00000010   Code   RO         2176    i.prvTaskExitError  port.o
    0x08003648   0x08003648   0x000002c8   Code   RO         2732    i.prvTimerTask      timers.o
    0x08003910   0x08003910   0x0000006c   Code   RO         2246    i.prvUnlockQueue    queue.o
    0x0800397c   0x0800397c   0x000000f8   Code   RO         2077    i.pvPortMalloc      heap_4.o
    0x08003a74   0x08003a74   0x0000002c   Code   RO         2177    i.pxPortInitialiseStack  port.o
    0x08003aa0   0x08003aa0   0x00000004   Code   RO         1739    i.read_addr         usmart.o
    0x08003aa4   0x08003aa4   0x0000012c   Code   RO          194    i.sys_check_rst     sys.o
    0x08003bd0   0x08003bd0   0x00000128   Code   RO          195    i.sys_clock_set     sys.o
    0x08003cf8   0x08003cf8   0x0000000c   Code   RO          196    i.sys_get_tick      sys.o
    0x08003d04   0x08003d04   0x00000070   Code   RO          200    i.sys_nvic_init     sys.o
    0x08003d74   0x08003d74   0x00000010   Code   RO          202    i.sys_soft_reset    sys.o
    0x08003d84   0x08003d84   0x00000048   Code   RO          204    i.sys_stm32_clock_init  sys.o
    0x08003dcc   0x08003dcc   0x00000054   Code   RO         1449    i.task_test         main.o
    0x08003e20   0x08003e20   0x0000015c   Code   RO          684    i.tim_cap_irq_callback  tim.o
    0x08003f7c   0x08003f7c   0x00000274   Code   RO          687    i.tim_cnt_pin_init  tim.o
    0x080041f0   0x080041f0   0x000000a8   Code   RO          688    i.tim_cnt_set_value  tim.o
    0x08004298   0x08004298   0x0000007c   Code   RO          689    i.tim_int_callback_deregister  tim.o
    0x08004314   0x08004314   0x000000b8   Code   RO          690    i.tim_int_callback_register  tim.o
    0x080043cc   0x080043cc   0x00000224   Code   RO          691    i.tim_pwm_pin_init  tim.o
    0x080045f0   0x080045f0   0x000000c4   Code   RO          692    i.tim_pwm_set_ccr   tim.o
    0x080046b4   0x080046b4   0x0000000c   Code   RO         1395    i.track_get_sensor_data  track.o
    0x080046c0   0x080046c0   0x0000000a   Code   RO         1396    i.track_init        track.o
    0x080046ca   0x080046ca   0x00000002   PAD
    0x080046cc   0x080046cc   0x00000028   Code   RO         1397    i.track_read_sensors  track.o
    0x080046f4   0x080046f4   0x0000015c   Code   RO          384    i.usart_init        usart.o
    0x08004850   0x08004850   0x0000000c   Code   RO          386    i.usart_iqr_callback_register  usart.o
    0x0800485c   0x0800485c   0x00000088   Code   RO          388    i.usart_send_data   usart.o
    0x080048e4   0x080048e4   0x000000b0   Code   RO         1740    i.usmart_cmd_rec    usmart.o
    0x08004994   0x08004994   0x00000280   Code   RO         1741    i.usmart_exe        usmart.o
    0x08004c14   0x08004c14   0x000000b6   Code   RO         1890    i.usmart_get_aparm  usmart_str.o
    0x08004cca   0x08004cca   0x00000042   Code   RO         1891    i.usmart_get_cmdname  usmart_str.o
    0x08004d0c   0x08004d0c   0x000001b4   Code   RO         1892    i.usmart_get_fname  usmart_str.o
    0x08004ec0   0x08004ec0   0x00000308   Code   RO         1893    i.usmart_get_fparam  usmart_str.o
    0x080051c8   0x080051c8   0x0000001c   Code   RO         1833    i.usmart_get_input_string  usmart_port.o
    0x080051e4   0x080051e4   0x00000058   Code   RO         1894    i.usmart_get_parmpos  usmart_str.o
    0x0800523c   0x0800523c   0x00000020   Code   RO         1742    i.usmart_init       usmart.o
    0x0800525c   0x0800525c   0x00000044   Code   RO         1834    i.usmart_recv_func  usmart_port.o
    0x080052a0   0x080052a0   0x000000a4   Code   RO         1743    i.usmart_scan       usmart.o
    0x08005344   0x08005344   0x00000112   Code   RO         1897    i.usmart_str2num    usmart_str.o
    0x08005456   0x08005456   0x0000001a   Code   RO         1898    i.usmart_strcmp     usmart_str.o
    0x08005470   0x08005470   0x0000069c   Code   RO         1744    i.usmart_sys_cmd_exe  usmart.o
    0x08005b0c   0x08005b0c   0x00000034   Code   RO         1835    i.usmart_timx_get_time  usmart_port.o
    0x08005b40   0x08005b40   0x00000058   Code   RO         1836    i.usmart_timx_init  usmart_port.o
    0x08005b98   0x08005b98   0x00000030   Code   RO         1837    i.usmart_timx_reset_time  usmart_port.o
    0x08005bc8   0x08005bc8   0x00000024   Code   RO         2139    i.uxListRemove      list.o
    0x08005bec   0x08005bec   0x00000016   Code   RO         2140    i.vListInitialise   list.o
    0x08005c02   0x08005c02   0x00000006   Code   RO         2141    i.vListInitialiseItem  list.o
    0x08005c08   0x08005c08   0x0000003a   Code   RO         2142    i.vListInsert       list.o
    0x08005c42   0x08005c42   0x00000018   Code   RO         2143    i.vListInsertEnd    list.o
    0x08005c5a   0x08005c5a   0x00000002   PAD
    0x08005c5c   0x08005c5c   0x0000001c   Code   RO         2179    i.vPortEnterCritical  port.o
    0x08005c78   0x08005c78   0x00000018   Code   RO         2180    i.vPortExitCritical  port.o
    0x08005c90   0x08005c90   0x0000008c   Code   RO         2078    i.vPortFree         heap_4.o
    0x08005d1c   0x08005d1c   0x00000018   Code   RO         2181    i.vPortSetupTimerInterrupt  port.o
    0x08005d34   0x08005d34   0x0000004a   Code   RO         2256    i.vQueueWaitForMessageRestricted  queue.o
    0x08005d7e   0x08005d7e   0x00000002   PAD
    0x08005d80   0x08005d80   0x00000038   Code   RO         2426    i.vTaskDelay        tasks.o
    0x08005db8   0x08005db8   0x000000d0   Code   RO         2427    i.vTaskDelete       tasks.o
    0x08005e88   0x08005e88   0x00000010   Code   RO         2431    i.vTaskInternalSetTimeOutState  tasks.o
    0x08005e98   0x08005e98   0x00000184   Code   RO         2432    i.vTaskListTasks    tasks.o
    0x0800601c   0x0800601c   0x0000000c   Code   RO         2433    i.vTaskMissedYield  tasks.o
    0x08006028   0x08006028   0x00000020   Code   RO         2434    i.vTaskPlaceOnEventList  tasks.o
    0x08006048   0x08006048   0x00000040   Code   RO         2435    i.vTaskPlaceOnEventListRestricted  tasks.o
    0x08006088   0x08006088   0x00000074   Code   RO         2442    i.vTaskStartScheduler  tasks.o
    0x080060fc   0x080060fc   0x00000010   Code   RO         2443    i.vTaskSuspendAll   tasks.o
    0x0800610c   0x0800610c   0x0000004c   Code   RO         2444    i.vTaskSwitchContext  tasks.o
    0x08006158   0x08006158   0x00000004   Code   RO         1745    i.write_addr        usmart.o
    0x0800615c   0x0800615c   0x0000000c   Code   RO         2082    i.xPortGetFreeHeapSize  heap_4.o
    0x08006168   0x08006168   0x00000040   Code   RO         2182    i.xPortStartScheduler  port.o
    0x080061a8   0x080061a8   0x0000002c   Code   RO         2183    i.xPortSysTickHandler  port.o
    0x080061d4   0x080061d4   0x00000094   Code   RO         2258    i.xQueueGenericCreate  queue.o
    0x08006268   0x08006268   0x00000114   Code   RO         2267    i.xQueueReceive     queue.o
    0x0800637c   0x0800637c   0x00000058   Code   RO         2446    i.xTaskCheckForTimeOut  tasks.o
    0x080063d4   0x080063d4   0x000001a4   Code   RO         2447    i.xTaskCreate       tasks.o
    0x08006578   0x08006578   0x0000005c   Code   RO         2448    i.xTaskDelayUntil   tasks.o
    0x080065d4   0x080065d4   0x0000001c   Code   RO         2455    i.xTaskGetSchedulerState  tasks.o
    0x080065f0   0x080065f0   0x0000000c   Code   RO         2456    i.xTaskGetTickCount  tasks.o
    0x080065fc   0x080065fc   0x00000178   Code   RO         2458    i.xTaskIncrementTick  tasks.o
    0x08006774   0x08006774   0x000000e8   Code   RO         2461    i.xTaskRemoveFromEventList  tasks.o
    0x0800685c   0x0800685c   0x00000154   Code   RO         2462    i.xTaskResumeAll    tasks.o
    0x080069b0   0x080069b0   0x00000070   Code   RO         2741    i.xTimerCreateTimerTask  timers.o
    0x08006a20   0x08006a20   0x00000480   Data   RO           29    .constdata          gpio.o
    0x08006ea0   0x08006ea0   0x0000012d   Data   RO          636    .constdata          adc.o
    0x08006fcd   0x08006fcd   0x0000001a   Data   RO          694    .constdata          tim.o
    0x08006fe7   0x08006fe7   0x00000008   Data   RO          870    .constdata          led.o
    0x08006fef   0x08006fef   0x000005f0   Data   RO         1195    .constdata          oled_data.o
    0x080075df   0x080075df   0x0000023a   Data   RO         1196    .constdata          oled_data.o
    0x08007819   0x08007819   0x000000f5   Data   RO         1197    .constdata          oled_data.o
    0x0800790e   0x0800790e   0x00000012   Data   RO         1241    .constdata          gpio_key.o
    0x08007920   0x08007920   0x00000006   Data   RO         1360    .constdata          encoder.o
    0x08007926   0x08007926   0x00000002   PAD
    0x08007928   0x08007928   0x0000000c   Data   RO         1677    .constdata          malloc.o
    0x08007934   0x08007934   0x00000028   Data   RO         1746    .conststring        usmart.o
    0x0800795c   0x0800795c   0x000000b0   Data   RO         1806    .conststring        usmart_config.o
    0x08007a0c   0x08007a0c   0x00000020   Data   RO         3259    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08007a2c, Size: 0x00010530, Max: 0x00020000, ABSOLUTE, COMPRESSED[0x0000006c])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000008   Data   RW          205    .data               sys.o
    0x20000008   COMPRESSED   0x00000004   Data   RW          342    .data               delay.o
    0x2000000c   COMPRESSED   0x00000008   Data   RW          486    .data               i2c.o
    0x20000014   COMPRESSED   0x00000001   Data   RW          637    .data               adc.o
    0x20000015   COMPRESSED   0x00000001   PAD
    0x20000016   COMPRESSED   0x00000004   Data   RW          695    .data               tim.o
    0x2000001a   COMPRESSED   0x0000000a   Data   RW         1242    .data               gpio_key.o
    0x20000024   COMPRESSED   0x00000001   Data   RW         1399    .data               track.o
    0x20000025   COMPRESSED   0x00000001   Data   RW         1528    .data               app_key.o
    0x20000026   COMPRESSED   0x00000002   Data   RW         1588    .data               app_state.o
    0x20000028   COMPRESSED   0x00000001   Data   RW         1629    .data               app_ui.o
    0x20000029   COMPRESSED   0x00000003   PAD
    0x2000002c   COMPRESSED   0x00000014   Data   RW         1678    .data               malloc.o
    0x20000040   COMPRESSED   0x0000001c   Data   RW         1747    .data               usmart.o
    0x2000005c   COMPRESSED   0x00000028   Data   RW         1807    .data               usmart_config.o
    0x20000084   COMPRESSED   0x000000f4   Data   RW         1808    .data               usmart_config.o
    0x20000178   COMPRESSED   0x00000002   Data   RW         1839    .data               usmart_port.o
    0x2000017a   COMPRESSED   0x00000002   PAD
    0x2000017c   COMPRESSED   0x0000001c   Data   RW         2085    .data               heap_4.o
    0x20000198   COMPRESSED   0x00000004   Data   RW         2184    .data               port.o
    0x2000019c   COMPRESSED   0x00000040   Data   RW         2464    .data               tasks.o
    0x200001dc   COMPRESSED   0x00000014   Data   RW         2750    .data               timers.o
    0x200001f0        -       0x00000040   Zero   RW           28    .bss                gpio.o
    0x20000230        -       0x0000001c   Zero   RW          389    .bss                usart.o
    0x2000024c        -       0x00000030   Zero   RW          635    .bss                adc.o
    0x2000027c        -       0x000000e4   Zero   RW          693    .bss                tim.o
    0x20000360        -       0x00000420   Zero   RW          917    .bss                debug.o
    0x20000780        -       0x00000400   Zero   RW         1017    .bss                oled.o
    0x20000b80        -       0x00000100   Zero   RW         1587    .bss                app_state.o
    0x20000c80        -       0x00009940   Zero   RW         1675    .bss                malloc.o
    0x2000a5c0        -       0x00000994   Zero   RW         1676    .bss                malloc.o
    0x2000af54        -       0x00000100   Zero   RW         1838    .bss                usmart_port.o
    0x2000b054        -       0x00005000   Zero   RW         2084    .bss                heap_4.o
    0x20010054        -       0x000000b4   Zero   RW         2463    .bss                tasks.o
    0x20010108        -       0x00000028   Zero   RW         2749    .bss                timers.o
    0x20010130        -       0x00000400   Zero   RW            1    STACK               startup_stm32f407xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       644        104        301          1         48       3488   adc.o
         0          0          0          0          0        648   adc_key.o
       188         56          0          1          0       3985   app_key.o
       404        212          0          2        256       2040   app_state.o
       180         50          0          1          0       1419   app_ui.o
       628        152          0          0       1056       4727   debug.o
       144         14          0          4          0       2451   delay.o
       100          8          0          0          0       1572   dma.o
        60          6          6          0          0       1240   encoder.o
         0          0          0          0          0      15340   event_groups.o
      1396        116       1152          0         64     572647   gpio.o
       392         26         18         10          0       2965   gpio_key.o
       400         22          0         28      20480       4886   heap_4.o
      1240        178          0          8          0       6567   i2c.o
        32          6          0          0          0       1810   key.o
        72          8          8          0          0       1753   led.o
       146          0          0          0          0       3458   list.o
       706        346          0          0          0      33208   main.o
       116         16         12         20      41684       3382   malloc.o
      1418         34          0          0       1024       8438   oled.o
         0          0       2335          0          0       1110   oled_data.o
       434         52          0          4          0       9684   port.o
       606          4          0          0          0       9572   queue.o
        44         10        392          0       1024        956   startup_stm32f407xx.o
       964        312          0          8          0      22087   sys.o
      3024        250          0         64        180      37981   tasks.o
      2532        422         26          4        228      14345   tim.o
      1196         56          0         20         40      14130   timers.o
        62         12          0          1          0       2186   track.o
       744        134          0          0         28       6553   usart.o
      2712       1312         40         28          0       6622   usmart.o
         0          0        176        284          0       1448   usmart_config.o
       344         82          0          2        256       4125   usmart_port.o
      1858         22          0          0          0      11171   usmart_str.o

    ----------------------------------------------------------------------
     22798       <USER>       <GROUP>        496      66368     817994   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        12          0          2          6          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        86          0          0          0          0          0   __dczerorl2.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
      2358         96          0          0          0        764   printfa.o
        28          0          0          0          0         76   strcmp.o
        18          0          0          0          0         68   strcpy.o
        14          0          0          0          0         68   strlen.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
       228          0          0          0          0         96   dmul.o

    ----------------------------------------------------------------------
      3978        <USER>          <GROUP>          0          0       2252   Library Totals
         6          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2906        112          0          0          0       1596   mc_w.l
      1066          0          0          0          0        656   mf_w.l

    ----------------------------------------------------------------------
      3978        <USER>          <GROUP>          0          0       2252   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     26776       4134       4500        496      66368     803434   Grand Totals
     26776       4134       4500        108      66368     803434   ELF Image Totals (compressed)
     26776       4134       4500        108          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                31276 (  30.54kB)
    Total RW  Size (RW Data + ZI Data)             66864 (  65.30kB)
    Total ROM Size (Code + RO Data + RW Data)      31384 (  30.65kB)

==============================================================================

