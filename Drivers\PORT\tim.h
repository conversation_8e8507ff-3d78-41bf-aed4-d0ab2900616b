#ifndef __TIM_H_
#define __TIM_H_

extern void (*g_timx_callback_funs[20])(void); // 定时器中断回调函数数组

/**
 * @brief       基本定时器TIMX定时中断回调函数注册
 * @param       callback: 定时器中断回调函数
 * @param       time: 执行时间间隔 单位ms
 * @retval      无
 */
void tim_int_callback_register(void (*callback)(void), uint16_t time);

/**
 * @brief       基本定时器TIMX定时中断回调函数注销
 * @param       callback: 定时器中断回调函数
 * @retval      无
 */
void tim_int_callback_deregister(void (*callback)(void));

/**
 * @brief       定时器TIMX PWM输出引脚初始化
 * @param       tim_id: 定时器ID
 * @param       channel: 通道号, 1~4
 * @param       pin: GPIO引脚位置, PA0~PI15
 * @param       arr: 自动重装值
 * @param       psc: 时钟预分频数
 * @retval      无
 */
void tim_pwm_pin_init(uint8_t tim_id, uint8_t channel, uint8_t pin, uint32_t arr, uint32_t psc);

/**
 * @brief       设置定时器TIMX的PWM通道的CCR值
 * @param       tim_id: 定时器ID
 * @param       channel: 通道号, 1~4
 * @param       ccr: CCR值, 0~65535
 * @retval      无
 */
void tim_pwm_set_ccr(uint8_t tim_id, uint8_t channel, uint16_t ccr);

/**
 * @brief       初始化定时器TIMX的脉冲计数
 * @param       tim_id: 定时器ID
 * @param       channel: 通道号, 1~4
 * @param       pin: 脉冲计数引脚
 * @param       mode: 脉冲计数模式, 0: 脉冲计数, 1: 编码器计数模式3
 * @retval      无
 */
void tim_cnt_pin_init(uint8_t tim_id, uint8_t channel, uint8_t pin, uint8_t mode);

/**
 * @brief       获取定时器TIMX的计数器值
 * @param       tim_id: 定时器ID
 * @retval      返回计数器值
 */
int32_t tim_cnt_get_value(uint8_t tim_id);

/**
 * @brief       设置定时器TIMX的计数器值
 * @param       tim_id: 定时器ID
 * @param       value: 要设置的计数器值
 * @retval      无
 */
void tim_cnt_set_value(uint8_t tim_id, uint32_t value);

/**
 * @brief       定时器TIMX输入捕获引脚初始化
 * @param       tim_id: 定时器ID
 * @param       channel: 通道号, 1~4
 * @param       pin: GPIO引脚位置, PA0~PI15
 * @param       arr: 自动重装值
 * @param       psc: 时钟预分频数
 * @retval      无
 */
void tim_cap_pin_init(uint8_t tim_id, uint8_t channel, uint8_t pin, uint16_t arr, uint16_t psc);

/**
 * @brief       获取定时器TIMX捕获的值
 * @param       tim_id: 定时器ID
 * @retval      返回捕获的值
 */
uint32_t tim_cap_get_value(uint8_t tim_id);

#endif
