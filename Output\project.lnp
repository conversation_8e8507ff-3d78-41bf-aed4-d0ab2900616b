--cpu=Cortex-M4.fp.sp
"..\output\startup_stm32f407xx.o"
"..\output\gpio.o"
"..\output\sys.o"
"..\output\delay.o"
"..\output\usart.o"
"..\output\i2c.o"
"..\output\dma.o"
"..\output\spi.o"
"..\output\adc.o"
"..\output\tim.o"
"..\output\wdg.o"
"..\output\led.o"
"..\output\debug.o"
"..\output\oled.o"
"..\output\oled_data.o"
"..\output\adc_key.o"
"..\output\gpio_key.o"
"..\output\key.o"
"..\output\tb6612.o"
"..\output\encoder.o"
"..\output\track.o"
"..\output\main.o"
"..\output\app_key.o"
"..\output\app_state.o"
"..\output\app_ui.o"
"..\output\malloc.o"
"..\output\usmart.o"
"..\output\usmart_config.o"
"..\output\usmart_port.o"
"..\output\usmart_str.o"
"..\output\croutine.o"
"..\output\event_groups.o"
"..\output\heap_4.o"
"..\output\list.o"
"..\output\port.o"
"..\output\queue.o"
"..\output\stream_buffer.o"
"..\output\tasks.o"
"..\output\timers.o"
--library_type=microlib --strict --scatter "..\Output\project.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "..\Output\project.map" -o ..\Output\project.axf