<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>Project</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pArmCC>5060960::V5.06 update 7 (build 960)::.\ARMCC</pArmCC>
      <pCCUsed>5060960::V5.06 update 7 (build 960)::.\ARMCC</pCCUsed>
      <uAC6>0</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>STM32F407ZGTx</Device>
          <Vendor>STMicroelectronics</Vendor>
          <PackID>Keil.STM32F4xx_DFP.2.15.0</PackID>
          <PackURL>http://www.keil.com/pack/</PackURL>
          <Cpu>IRAM(0x20000000,0x00020000) IRAM2(0x10000000,0x00010000) IROM(0x08000000,0x00100000) CPUTYPE("Cortex-M4") FPU2 CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0STM32F4xx_1024 -********** -********* -FP0($$Device:STM32F407ZGTx$CMSIS\Flash\STM32F4xx_1024.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:STM32F407ZGTx$Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:STM32F407ZGTx$CMSIS\SVD\STM32F40x.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>..\Output\</OutputDirectory>
          <OutputName>project</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>0</BrowseInformation>
          <ListingPath>..\Output\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -REMAP -MPU</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> -MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3></Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <hadIRAM2>1</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>4</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x100000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x100000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x10000000</StartAddress>
                <Size>0x10000</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>4</Optim>
            <oTime>1</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>3</v6Lang>
            <v6LangP>3</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>STM32F407xx</Define>
              <Undefine></Undefine>
              <IncludePath>..\Drivers\CMSIS\Device\ST\STM32F4xx\Include;..\Drivers\CMSIS\Include;..\Drivers;..\App;..\Middlewares;..\Middlewares\freertos\include</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>1</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x08000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>Driver/CMSIS</GroupName>
          <Files>
            <File>
              <FileName>startup_stm32f407xx.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\Drivers\CMSIS\Device\ST\STM32F4xx\Source\Templates\arm\startup_stm32f407xx.s</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Driver/PORT</GroupName>
          <Files>
            <File>
              <FileName>port.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Drivers\PORT\port.h</FilePath>
            </File>
            <File>
              <FileName>gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\PORT\gpio.c</FilePath>
            </File>
            <File>
              <FileName>sys.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\PORT\sys.c</FilePath>
            </File>
            <File>
              <FileName>delay.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\PORT\delay.c</FilePath>
            </File>
            <File>
              <FileName>usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\PORT\usart.c</FilePath>
            </File>
            <File>
              <FileName>i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\PORT\i2c.c</FilePath>
            </File>
            <File>
              <FileName>dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\PORT\dma.c</FilePath>
            </File>
            <File>
              <FileName>spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\PORT\spi.c</FilePath>
            </File>
            <File>
              <FileName>adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\PORT\adc.c</FilePath>
            </File>
            <File>
              <FileName>tim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\PORT\tim.c</FilePath>
            </File>
            <File>
              <FileName>wdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\PORT\wdg.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Driver/BSP</GroupName>
          <Files>
            <File>
              <FileName>led.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\BSP\LED\led.c</FilePath>
            </File>
            <File>
              <FileName>debug.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\BSP\DEBUG\debug.c</FilePath>
            </File>
            <File>
              <FileName>OLED.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\BSP\OLED\OLED.c</FilePath>
            </File>
            <File>
              <FileName>OLED_Data.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\BSP\OLED\OLED_Data.c</FilePath>
            </File>
            <File>
              <FileName>adc_key.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\BSP\KEY\adc_key.c</FilePath>
            </File>
            <File>
              <FileName>gpio_key.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\BSP\KEY\gpio_key.c</FilePath>
            </File>
            <File>
              <FileName>key.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\BSP\KEY\key.c</FilePath>
            </File>
            <File>
              <FileName>encoder.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\BSP\ENCODER\encoder.c</FilePath>
            </File>
            <File>
              <FileName>track.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\BSP\TRACK\track.c</FilePath>
            </File>
            <File>
              <FileName>jy60.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\BSP\JYxx\jy60.c</FilePath>
            </File>
            <File>
              <FileName>jyxx.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\BSP\JYxx\jyxx.c</FilePath>
            </File>
            <File>
              <FileName>tb6612.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\BSP\TB6612\tb6612.c</FilePath>
            </File>
            <File>
              <FileName>motor.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\BSP\MOTOR\motor.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>System</GroupName>
          <Files>
            <File>
              <FileName>config.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\System\config.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>App</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\App\main.c</FilePath>
            </File>
            <File>
              <FileName>app_key.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\App\app_key.c</FilePath>
            </File>
            <File>
              <FileName>app_state.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\App\app_state.c</FilePath>
            </File>
            <File>
              <FileName>app_ui.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\App\app_ui.c</FilePath>
            </File>
            <File>
              <FileName>app_motor.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\App\app_motor.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Middlewares/MALLOC</GroupName>
          <Files>
            <File>
              <FileName>malloc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Middlewares\MALLOC\malloc.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Middlewares/usmart</GroupName>
          <Files>
            <File>
              <FileName>usmart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Middlewares\USMART\usmart.c</FilePath>
            </File>
            <File>
              <FileName>usmart_config.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Middlewares\USMART\usmart_config.c</FilePath>
            </File>
            <File>
              <FileName>usmart_port.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Middlewares\USMART\usmart_port.c</FilePath>
            </File>
            <File>
              <FileName>usmart_str.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Middlewares\USMART\usmart_str.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Middlewares/freertos</GroupName>
          <Files>
            <File>
              <FileName>croutine.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Middlewares\freertos\src\croutine.c</FilePath>
            </File>
            <File>
              <FileName>event_groups.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Middlewares\freertos\src\event_groups.c</FilePath>
            </File>
            <File>
              <FileName>heap_4.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Middlewares\freertos\src\heap_4.c</FilePath>
            </File>
            <File>
              <FileName>list.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Middlewares\freertos\src\list.c</FilePath>
            </File>
            <File>
              <FileName>port.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Middlewares\freertos\src\port.c</FilePath>
            </File>
            <File>
              <FileName>queue.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Middlewares\freertos\src\queue.c</FilePath>
            </File>
            <File>
              <FileName>stream_buffer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Middlewares\freertos\src\stream_buffer.c</FilePath>
            </File>
            <File>
              <FileName>tasks.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Middlewares\freertos\src\tasks.c</FilePath>
            </File>
            <File>
              <FileName>timers.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Middlewares\freertos\src\timers.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Middlewares/pid</GroupName>
          <Files>
            <File>
              <FileName>pid.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Middlewares\pid\pid.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Middlewares/cJSON</GroupName>
          <Files>
            <File>
              <FileName>cJSON.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Middlewares\cJSON\cJSON.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Middlewares/lwrb</GroupName>
          <Files>
            <File>
              <FileName>lwrb.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Middlewares\lwrb\lwrb.c</FilePath>
            </File>
            <File>
              <FileName>lwrb_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Middlewares\lwrb\lwrb_ex.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components/>
    <files/>
  </RTE>

  <LayerInfo>
    <Layers>
      <Layer>
        <LayName>project</LayName>
        <LayPrjMark>1</LayPrjMark>
      </Layer>
    </Layers>
  </LayerInfo>

</Project>
