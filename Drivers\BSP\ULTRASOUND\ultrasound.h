#ifndef __ULTRASOUND_H_
#define __ULTRASOUND_H_

#include "./PORT/port.h"
typedef struct
{
    uint8_t edge_state;            // 边沿状态：0=等待上升沿，1=等待下降沿
    uint16_t tim_overflow_counter; // 定时器溢出计数器
    uint32_t t1;                   // 上升沿时间
    uint32_t t2;                   // 下降沿时间
    uint32_t high_level_us;        // 高电平持续时间
    float distance;                // 距离值(cm)
} Hcsr04InfoTypeDef;
/**
 * @brief       初始化超声波模块
 * @param       无
 * @retval      无
 */
void ultrasound_init(void);

/**
 * @brief       触发超声波测距并读取距离
 * @param       无
 * @retval      距离值(单位:cm)，返回0表示测量失败
 */
float ultrasound_read_distance(void);

/**
 * @brief       触发超声波测距
 * @param       无
 * @retval      无
 */
void ultrasound_start(void);

/**
 * @brief       读取距离值
 * @param       无
 * @retval      距离值(单位:cm)
 */
float ultrasound_read(void);

/**
 * @brief       定时器溢出中断处理函数
 * @param       无
 * @retval      无
 */
void ultrasound_tim_overflow_handler(void);

/**
 * @brief       输入捕获中断处理函数
 * @param       无
 * @retval      无
 */
void ultrasound_tim_ic_handler(void);

#endif /* __ULTRASOUND_H_ */
