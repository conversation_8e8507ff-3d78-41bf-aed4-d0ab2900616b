#include "./BSP/ULTRASOUND/ultrasound.h"
#include "./PORT/port.h"
#include "../System/config.h"

#if CFG_ULTRASOUND_EN == 1

// 超声波传感器状态定义
#define ULTRASOUND_IDLE 0      // 空闲状态
#define ULTRASOUND_MEASURING 1 // 测量中

// 超声波测量相关变量
static uint8_t ultrasound_status[CFG_ULTRASOUND_NUM];      // 传感器状态
static uint32_t ultrasound_start_time[CFG_ULTRASOUND_NUM]; // 开始时间
static uint16_t ultrasound_distance[CFG_ULTRASOUND_NUM];   // 距离值
Hcsr04InfoTypeDef Hcsr04Info;
/**
 * @brief       初始化超声波模块
 * @param       无
 * @retval      无
 */
void ultrasound_init(void)
{
    // ECHO引脚初始化
    tim_cap_pin_init(CFG_ULTRASOUND_ECHO_TIM_ID, CFG_ULTRASOUND_ECHO_TIM_CHANNEL, CFG_ULTRASOUND_ECHO_PIN, CFG_ULTRASOUND_ECHO_TIM_ARR, CFG_ULTRASOUND_ECHO_TIM_PSC);
    // TRIG引脚初始化
    gpio_pin_init(CFG_ULTRASOUND_TRIG_PIN, GPIO_MODE_OUT, GPIO_OTYPE_PP, GPIO_SPEED_LOW, GPIO_PUPD_NONE);

    tim_int_callback_register(ultrasound_echo_ic_handler, 300);
}
void ultrasound_echo_ic_handler(void)
{
    if (Hcsr04Info.edge_state == 0) //  捕获上升沿
    {
        Hcsr04Info.t1 = tim_cap_get_value(CFG_ULTRASOUND_ECHO_TIM_ID);
        Hcsr04Info.edge_state = 1;
    }
    else //  捕获下降沿
    {
        Hcsr04Info.t2 = tim_cap_get_value(CFG_ULTRASOUND_ECHO_TIM_ID);
        Hcsr04Info.edge_state = 0;
}

/**
 * @brief       获取超声波距离
 * @param       sensor_id: 传感器编号 (0~CFG_ULTRASOUND_NUM-1)
 * @retval      距离值(单位:cm)，返回0表示测量失败
 */
uint16_t ultrasound_get_distance(uint8_t sensor_id)
{
    if (sensor_id >= CFG_ULTRASOUND_NUM)
        return 0;

    return ultrasound_distance[sensor_id];
}

/**
 * @brief       触发超声波测距
 * @param       sensor_id: 传感器编号 (0~CFG_ULTRASOUND_NUM-1)
 * @retval      无
 */
void ultrasound_trigger(uint8_t sensor_id)
{
    if (sensor_id >= CFG_ULTRASOUND_NUM)
        return;

    if (ultrasound_status[sensor_id] != ULTRASOUND_IDLE)
        return; // 如果正在测量中，则不触发新的测量

    // 发送触发脉冲：高电平持续至少10us
    gpio_write_pin(ultrasound_trig_pin[sensor_id], 1);
    delay_us(12); // 延时12us
    gpio_write_pin(ultrasound_trig_pin[sensor_id], 0);

    // 设置状态为测量中
    ultrasound_status[sensor_id] = ULTRASOUND_MEASURING;
    ultrasound_distance[sensor_id] = 0; // 清除上次测量结果
}

/**
 * @brief       超声波回声中断处理函数
 * @param       sensor_id: 传感器编号 (0~CFG_ULTRASOUND_NUM-1)
 * @retval      无
 * @note        此函数应在外部中断服务程序中调用
 */
void ultrasound_echo_handler(uint8_t sensor_id)
{
    if (sensor_id >= CFG_ULTRASOUND_NUM)
        return;

    // 读取当前回声引脚状态
    uint8_t echo_state = gpio_read_pin(ultrasound_echo_pin[sensor_id]);

    if (echo_state == 1) // 上升沿，开始计时
    {
        ultrasound_start_time[sensor_id] = get_tick_us(); // 获取当前时间(微秒)
    }
    else // 下降沿，结束计时并计算距离
    {
        if (ultrasound_status[sensor_id] == ULTRASOUND_MEASURING)
        {
            uint32_t echo_time = get_tick_us() - ultrasound_start_time[sensor_id];

            // 计算距离：距离 = (回声时间 * 声速) / 2
            // 声速约为340m/s = 0.034cm/us
            // 距离(cm) = echo_time(us) * 0.034 / 2 = echo_time * 0.017
            ultrasound_distance[sensor_id] = (uint16_t)(echo_time * 17 / 1000);

            // 限制测量范围 (2cm ~ 400cm)
            if (ultrasound_distance[sensor_id] < 2)
                ultrasound_distance[sensor_id] = 0; // 测量失败
            else if (ultrasound_distance[sensor_id] > 400)
                ultrasound_distance[sensor_id] = 400; // 超出量程

            ultrasound_status[sensor_id] = ULTRASOUND_IDLE; // 测量完成，恢复空闲状态
        }
    }
}

/**
 * @brief       获取超声波模块状态
 * @param       sensor_id: 传感器编号 (0~CFG_ULTRASOUND_NUM-1)
 * @retval      0:空闲 1:测量中
 */
uint8_t ultrasound_get_status(uint8_t sensor_id)
{
    if (sensor_id >= CFG_ULTRASOUND_NUM)
        return ULTRASOUND_IDLE;

    return ultrasound_status[sensor_id];
}

#endif
