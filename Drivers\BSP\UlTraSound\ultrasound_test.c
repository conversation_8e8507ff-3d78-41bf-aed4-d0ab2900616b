/**
 * 超声波驱动测试示例
 * 使用方法：
 * 1. 在main.c中包含此头文件
 * 2. 在main函数中调用ultrasound_test()函数
 */

#include "./BSP/ULTRASOUND/ultrasound.h"
#include "./BSP/DEBUG/debug.h"
#include "../System/config.h"

#if CFG_ULTRASOUND_EN == 1

/**
 * @brief       超声波测试函数
 * @param       无
 * @retval      无
 */
void ultrasound_test(void)
{
    float distance;
    
    // 初始化超声波模块
    ultrasound_init();
    
    printf("超声波测试开始...\r\n");
    
    while (1)
    {
        // 读取距离
        distance = ultrasound_read_distance();
        
        // 打印距离值
        printf("距离: %.2f cm\r\n", distance);
        
        // 延时1秒
        delay_ms(1000);
    }
}

/**
 * @brief       超声波单次测试函数
 * @param       无
 * @retval      距离值(cm)
 */
float ultrasound_single_test(void)
{
    float distance;
    
    // 触发测距
    ultrasound_start();
    
    // 等待测量完成
    delay_ms(60);
    
    // 读取距离
    distance = ultrasound_read();
    
    return distance;
}

#endif
