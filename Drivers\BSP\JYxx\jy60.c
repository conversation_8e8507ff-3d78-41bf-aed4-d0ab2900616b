#include "./PORT/port.h"
#include "../System/config.h"

#include "./BSP/JYxx/jy60.h"

#include "string.h"

// 状态变量优化
static uint8_t jy60_sleep_state = 0; //     JY60是否处于休眠状态
static uint8_t jy60_data_ready = 0;  // 数据是否准备好

// 缓冲区优化
static uint8_t jy60_raw_buffer[33];       //    接收到的原始数据
static uint8_t jy60_processed_buffer[33]; //    处理后的数据
static uint8_t jy60_buffer_index = 0;     //    缓冲区索引

// 数据变量优化
static float acceleration[3] = {0}; // 加速度
static float angular_vel[3] = {0};  // 角速度
static float euler_angles[3] = {0}; // 角度

static float temperature;  // 温度
static float voltage;      // 电压
static float firmware_ver; // 固件版本

/**
 * @brief       向JY60发送指令
 * @param       cmd: 指令
 * @retval      无
 */
void jy60_send_command(uint8_t cmd)
{
    uint8_t command[3] = {0xFF, 0xAA, cmd};
    usart_send_data(CFG_JYxx_UART_ID, command, 3);
}

/**
 * @brief       设置JY60休眠或唤醒
 * @param       state: 休眠或唤醒   1:休眠   0:唤醒
 * @retval      无
 */
void jy60_set_sleep_wake(uint8_t state)
{
    if (state == 1 && jy60_sleep_state == 0)
    {
        jy60_sleep_state = 1;
        jy60_send_command(CMD_WAKE_OR_SLEEP);
    }
    else if (state == 0 && jy60_sleep_state == 1)
    {
        jy60_sleep_state = 0;
        jy60_send_command(CMD_WAKE_OR_SLEEP);
    }
}

/**
 * @brief       JY60串口接收回调函数
 * @param       data: 接收到的数据
 * @retval      无
 */
static void jy60_uart_recv_callback(uint8_t data)
{
    jy60_raw_buffer[jy60_buffer_index++] = data;

    // 包头检查
    if (jy60_buffer_index == 2)
    {
        if (!(jy60_raw_buffer[0] == TYPE_HEAD &&
              jy60_raw_buffer[1] == TYPE_ACCELERATED_SPEED))
        {
            jy60_buffer_index = 0;
            memset(jy60_raw_buffer, 0, 33);
            return;
        }
    }
    // 中间包检查
    else if (jy60_buffer_index == 13 && jy60_data_ready == 0)
    {
        if (!(jy60_raw_buffer[11] == TYPE_HEAD &&
              jy60_raw_buffer[12] == TYPE_ANGLE_SPEED))
        {
            jy60_buffer_index = 0;
            memset(jy60_raw_buffer, 0, 33);
            return;
        }
    }
    // 结束包检查
    else if (jy60_buffer_index == 24 && jy60_data_ready == 0)
    {
        if (!(jy60_raw_buffer[22] == TYPE_HEAD &&
              jy60_raw_buffer[23] == TYPE_ANGLE))
        {
            jy60_buffer_index = 0;
            memset(jy60_raw_buffer, 0, 33);
            return;
        }
    }
    // 完整帧处理
    else if (jy60_buffer_index == 33 && jy60_data_ready == 0)
    {
        jy60_data_ready = 1;
        memcpy(jy60_processed_buffer, jy60_raw_buffer, 33);
        jy60_buffer_index = 0;
    }
}

/**
 * @brief       更新JY60数据
 * @param       无
 * @retval      无
 */
void jy60_up_data(void)
{
    float temp;
    uint8_t sum;

    if (jy60_data_ready == 1)
    {
        for (uint8_t i = 0; i < 3; i++)
        {
            sum = 0;
            for (uint8_t j = 0; j < 10; j++)
            {
                sum += jy60_processed_buffer[j + i * 11];
            }

            if (i == 0) // 加速度数据
            {
                if (sum == jy60_processed_buffer[10 + i * 11])
                {
                    temp = (short)(((short)jy60_processed_buffer[3 + i * 11] << 8) | jy60_processed_buffer[2 + i * 11]);
                    acceleration[0] = temp / 32768.0f * 16.0f * 9.8f;

                    temp = (short)(((short)jy60_processed_buffer[5 + i * 11] << 8) | jy60_processed_buffer[4 + i * 11]);
                    acceleration[1] = temp / 32768.0f * 16.0f * 9.8f;

                    temp = (short)(((short)jy60_processed_buffer[7 + i * 11] << 8) | jy60_processed_buffer[6 + i * 11]);
                    acceleration[2] = temp / 32768.0f * 16.0f * 9.8f;

                    temp = (short)(((short)jy60_processed_buffer[9 + i * 11] << 8) | jy60_processed_buffer[8 + i * 11]);
                    temperature = temp / 32768.0f * 96.38f + 36.53f;
                }
            }
            else if (i == 1) // 角速度数据
            {
                if (sum == jy60_processed_buffer[10 + i * 11])
                {
                    temp = (short)(((short)jy60_processed_buffer[3 + i * 11] << 8) | jy60_processed_buffer[2 + i * 11]);
                    angular_vel[0] = temp / 32768.0f * 2000.0f;

                    temp = (short)(((short)jy60_processed_buffer[5 + i * 11] << 8) | jy60_processed_buffer[4 + i * 11]);
                    angular_vel[1] = temp / 32768.0f * 2000.0f;

                    temp = (short)(((short)jy60_processed_buffer[7 + i * 11] << 8) | jy60_processed_buffer[6 + i * 11]);
                    angular_vel[2] = temp / 32768.0f * 2000.0f;

                    temp = (short)(((short)jy60_processed_buffer[9 + i * 11] << 8) | jy60_processed_buffer[8 + i * 11]);
                    voltage = temp / 100.0f;
                }
            }
            else if (i == 2) // 欧拉角数据
            {
                if (sum == jy60_processed_buffer[10 + i * 11])
                {
                    temp = (short)(((short)jy60_processed_buffer[3 + i * 11] << 8) | jy60_processed_buffer[2 + i * 11]);
                    euler_angles[0] = temp / 32768.0f * 180.0f;

                    temp = (short)(((short)jy60_processed_buffer[5 + i * 11] << 8) | jy60_processed_buffer[4 + i * 11]);
                    euler_angles[1] = temp / 32768.0f * 180.0f;

                    temp = (short)(((short)jy60_processed_buffer[7 + i * 11] << 8) | jy60_processed_buffer[6 + i * 11]);
                    euler_angles[2] = temp / 32768.0f * 180.0f;

                    temp = (short)(((short)jy60_processed_buffer[9 + i * 11] << 8) | jy60_processed_buffer[8 + i * 11]);
                    firmware_ver = temp;
                }
            }
        }
        jy60_data_ready = 0;
    }
}

/**
 * @brief       获取JY60加速度
 * @param       加速度
 * @retval
 */
float *jy60_get_acceleration(void)
{
    return acceleration;
}

/**
 * @brief       获取JY60角速度
 * @param       无
 * @retval      角速度
 */
float *jy60_get_angle_speed(void)
{
    return angular_vel;
}

/**
 * @brief       获取JY60角度
 * @param       无
 * @retval      角度
 */
float *jy60_get_angle(void)
{
    return euler_angles;
}

/**
 * @brief       获取JY60温度
 * @param       无
 * @retval      温度
 */
float jy60_get_temperature(void)
{
    return temperature;
}

/**
 * @brief       获取JY60电压
 * @param       无
 * @retval      电压
 */
float jy60_get_voltage(void)
{
    return voltage;
}

/**
 * @brief       获取JY60版本
 * @param       无
 * @retval      版本
 */
float jy60_get_version(void)
{
    return firmware_ver;
}

/**
 * @brief       JY60初始化
 * @param       无
 * @retval      无
 */
void jy60_init(void)
{
    usart_init(CFG_JYxx_UART_ID, CFG_JYxx_UART_BAUD, CFG_JYxx_UART_TX_PIN, CFG_JYxx_UART_RX_PIN, 1);

    jy60_send_command(CMD_UART_MODE);
    jy60_send_command(CMD_UART_9600);
    jy60_send_command(CMD_HORIZONTAL);
    jy60_send_command(CMD_BANDWIDTH_42HZ);
    jy60_send_command(CMD_ZERO_ANGLE_Z);

    usart_iqr_callback_register(CFG_JYxx_UART_ID, jy60_uart_recv_callback);
}
